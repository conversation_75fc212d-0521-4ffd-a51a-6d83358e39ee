package com.flotomate.fs.agent.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flotomate.common.agent.AgentApplicationConfigRest;
import com.flotomate.common.agent.AgentDetailsRest;
import com.flotomate.common.agent.AgentRefreshInfoRest;
import com.flotomate.common.agent.AgentRefreshRest;
import com.flotomate.common.agent.AgentRest;
import com.flotomate.common.agent.ApplicationType;
import com.flotomate.common.agent.AssetLocatorQuery;
import com.flotomate.common.agent.OsVariant;
import com.flotomate.common.agent.ScannedPatchDataRest;
import com.flotomate.common.clients.FlotoCoreClientKeys;
import com.flotomate.common.discovery.GoDiscoveryConstants;
import com.flotomate.common.discovery.ParseUtil;
import com.flotomate.common.enums.FlotoManagedModels;
import com.flotomate.common.enums.OobType;
import com.flotomate.common.exception.FlotoError;
import com.flotomate.common.exception.FlotoErrorTemplates;
import com.flotomate.common.exception.FlotoException;
import com.flotomate.common.logger.DiscoveryLogger;
import com.flotomate.common.rest.asset.HardwareAssetRest;
import com.flotomate.common.rest.asset.OsArchitecture;
import com.flotomate.common.rest.asset.OsPlatform;
import com.flotomate.common.rest.auditItem.AdminAuditRest;
import com.flotomate.common.rest.auditItem.AuditEvent;
import com.flotomate.common.rest.auditItem.PerformerType;
import com.flotomate.common.rest.base.FlotoErrorResponse;
import com.flotomate.common.rest.command.CommandRest;
import com.flotomate.common.rest.command.CommandState;
import com.flotomate.common.rest.command.RCWorkType;
import com.flotomate.common.rest.credential.DiscoveryDeviceProtocol;
import com.flotomate.common.rest.flotofile.FlotoFileType;
import com.flotomate.common.rest.patch.AgentApplicationRest;
import com.flotomate.common.rest.patch.OsApplicationType;
import com.flotomate.common.rest.patch.PatchConstants;
import com.flotomate.common.rest.patch.PatchUtils;
import com.flotomate.common.rest.patch.Version;
import com.flotomate.common.rest.patch.linux.LinuxOsApplicationRest;
import com.flotomate.common.rest.patch.msrc.OsApplicationRest;
import com.flotomate.common.rest.patch.msrc.ProductType;
import com.flotomate.common.rest.qual.RelationalOperator;
import com.flotomate.common.rest.tenant.portal.LicenseManagedModules;
import com.flotomate.common.utils.FlotoStringUtils;
import com.flotomate.fs.agent.AgentBaseServiceImpl;
import com.flotomate.fs.agent.command.logdownload.UploadDownloadCommandCreator;
import com.flotomate.fs.agent.locator.LocatorService;
import com.flotomate.fs.agentpatch.AgentPatchCrossService;
import com.flotomate.fs.agentpatch.SysInfo;
import com.flotomate.fs.agentpatch.SysInfoUtils;
import com.flotomate.model.agent.Agent;
import com.flotomate.model.agent.AgentApplicationConfig;
import com.flotomate.model.agent.AgentApplicationConfig_;
import com.flotomate.model.agent.AgentBase_;
import com.flotomate.model.agent.AgentSetting;
import com.flotomate.model.agent.Agent_;
import com.flotomate.model.command.Command;
import com.flotomate.model.command.Command_;
import com.flotomate.models.Constants;
import com.flotomate.models.FlotoDiffObjMap;
import com.flotomate.models.FlotoMessageDestinations;
import com.flotomate.models.FlotoPageRequest;
import com.flotomate.models.agent.credential.CredentialProfile_;
import com.flotomate.models.asset.HardwareAsset;
import com.flotomate.models.asset.component.SoftwareComponent_;
import com.flotomate.models.base.FlotoBase_;
import com.flotomate.models.base.FlotoModelPart_;
import com.flotomate.models.base.FlotoObjectPage;
import com.flotomate.models.context.CallContext;
import com.flotomate.models.entity.FlotoEntity;
import com.flotomate.models.event.AgentScannedPatchDataEventMessage;
import com.flotomate.models.organization.Organization;
import com.flotomate.models.patch.AgentApplication;
import com.flotomate.models.patch.AgentApplication_;
import com.flotomate.models.patch.OsApplication;
import com.flotomate.models.patch.linux.LinuxOsApplication;
import com.flotomate.models.patch.redhat.satellite.RedHatSatelliteRepo;
import com.flotomate.models.qual.Qualification;
import com.flotomate.models.tenant.TenantPreference;
import com.flotomate.models.user.FlotoUser_;
import com.flotomate.models.value.part.AdminValue;
import com.flotomate.service.admin.audit.service.AdminAuditService;
import com.flotomate.service.agent.AgentService;
import com.flotomate.service.agent.application.AgentApplicationConfigService;
import com.flotomate.service.agent.credentialprofile.CredentialProfileService;
import com.flotomate.service.agent.setting.AgentLogDownloadHistoryService;
import com.flotomate.service.agent.setting.AgentSettingService;
import com.flotomate.service.audit.service.AbstractAuditService;
import com.flotomate.service.base.base.FlotoBaseService;
import com.flotomate.service.base.base.ServiceResolverService;
import com.flotomate.service.base.repository.FlotoBaseRepository;
import com.flotomate.service.base.repository.RepositoryResolverService;
import com.flotomate.service.cmdb.citype.CiTypeService;
import com.flotomate.service.command.service.CommandService;
import com.flotomate.service.exception.FlotoExceptionHandler;
import com.flotomate.service.file.FileStorageService;
import com.flotomate.service.lbcs.SystemProcessUtils;
import com.flotomate.service.organization.OrganizationService;
import com.flotomate.service.patch.redhatsatellite.RedHatSatelliteRepoService;
import com.flotomate.service.sequence.SequenceService;
import com.flotomate.service.tags.TagService;
import com.flotomate.service.user.FlotoUserService;
import com.flotomate.service.utility.FlotoUtils;
import com.flotomate.service.utility.FlotoValueUtils;
import com.flotomate.service.utility.QualUtils;
import com.flotomate.util.NsqProducerUtil;
import com.flotomate.util.zmq.FlotoZmqService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.FileTime;
import java.util.*;
import java.util.regex.MatchResult;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

@Service
public class AgentServiceImpl extends AgentBaseServiceImpl<Agent, AgentRest> implements AgentService {

    @Autowired
    ApplicationContext appContext;

    @Autowired
    protected TagService tagService;

    @Autowired
    OrganizationService organizationService;

    @Autowired
    AgentLocatorService agentLocatorService;

    @Autowired
    LocatorService locatorService;

    @Autowired
    CommandService commandService;

    @Autowired
    SequenceService sequenceService;

    @Autowired
    AgentPatchCrossService agentPatchCrossService;

    @Autowired
    ServiceResolverService serviceResolverService;

    @Autowired
    FlotoUserService userService;

    @Autowired
    RepositoryResolverService repositoryResolverService;

    @Autowired
    FlotoExceptionHandler exceptionHandler;
    @Autowired
    AdminAuditService adminAuditService;
    @Autowired
    AbstractAuditService abstractAuditService;
    @Autowired
    AgentRepository agentRepo;
    @Autowired
    FileStorageService fileStorageService;
    Logger discoveryLogger = LoggerFactory.getLogger(DiscoveryLogger.class);
    @Value("${com.flotomate.support.agent.version}")
    private String supportedAgentVersion;

    @Autowired
    FlotoZmqService flotoZmqService;

    @Autowired
    private Environment environment;

    @Value("${com.flotomate.go.runtime.version:1.22.6}")
    private String goRuntimeVersion;

    public static Map<OsPlatform, Map<Long, String>> patternExecutorHashCodeMap =
            new EnumMap<>(OsPlatform.class);

    @Autowired
    UploadDownloadCommandCreator uploadDownloadCommandCreator;

    @Autowired
    AgentLogDownloadHistoryService agentLogDownloadHistoryService;

    @Autowired
    AgentSettingService agentSettingService;

    private final AgentApplicationConfigService agentApplicationConfigService;

    @Value("${com.flotomate.support.secure.agent.version:8.5.1}")
    private String validAgentVersion;

    AgentServiceImpl selfProxy;

    @Autowired
    private CiTypeService ciTypeService;

    @Autowired
    RedHatSatelliteRepoService redHatSatelliteRepoService;

    @PostConstruct
    public void init() {
        selfProxy = appContext.getBean(AgentServiceImpl.class);
    }

    private final CredentialProfileService credentialProfileService;

    public AgentServiceImpl(AgentRepository repository, CredentialProfileService credentialProfileService,
            AgentApplicationConfigService agentApplicationConfigService,
            CredentialProfileService credentialProfileService1) {
        super(repository, Agent.class, credentialProfileService);
        this.agentApplicationConfigService = agentApplicationConfigService;
        this.credentialProfileService = credentialProfileService1;
    }

    @Override
    protected Agent convertToDomain(CallContext callContext, AgentRest createRequest) {
        Agent agent = super.convertToDomain(callContext, createRequest);

        // setter-getter
        agent.setName(createRequest.getHostName());
        agent.setAgentVersion(createRequest.getAgentVersion());
        agent.setServicePack(createRequest.getServicePack());
        agent.setLoggedOnUser(createRequest.getLoggedOnUser());
        agent.setSysInfo(createRequest.getSysInfo());
        agent.setHealthType(createRequest.getHealthType());
        agent.setOsVersion(createRequest.getOsVersion());
        agent.setBiosSerialNumber(createRequest.getBiosSerialNumber());
        agent.setPollerId(createRequest.getPollerId());
        agent.setLastLoggedInUser(createRequest.getLastLoggedInUser());
        if (createRequest.getRemoteOfficeId() <= 0) {
            FlotoBaseService<?, ?> service =
                    serviceResolverService.getRelevantServiceByManagedModel(FlotoManagedModels.REMOTE_OFFICE);
            if (service != null) {

                List<Long> localOfficeId = service.getAllIdsByQual(callContext,
                        QualUtils.buildRelationalQual("oobType", RelationalOperator.Equal,
                                OobType.SYSTEM_SETUP_DATA), null);
                if (CollectionUtils.isNotEmpty(localOfficeId)) {
                    agent.setRemoteOfficeId(localOfficeId.get(0));
                }
            }
        } else {
            agent.setRemoteOfficeId(createRequest.getRemoteOfficeId());
        }
        agent.setPatchEnabled(createRequest.isPatchEnabled());
        agent.setTags(createRequest.getTags());
        agent.setOsAppIds(createRequest.getOsAppIds());
        agent.setLastPatchScanTime(createRequest.getLastPatchScanTime());
        agent.setLanguage(createRequest.getLanguage());
        agent.setRebootRequired(createRequest.isRebootRequired());
        agent.setLastRebootTime(createRequest.getLastRebootTime());
        agent.setOsVersionNumber(createRequest.getOsVersionNumber());
        return agent;
    }

    @Override
    public Agent updatePatchScanStatus(CallContext callContext, CommandState patchScanStatus, long agentId) {
        Agent agent = getById(callContext, agentId, false);
        agent.setPatchscanstatus(patchScanStatus);
        Agent domainObj = agentRepo.save(callContext, agent);
        serviceLogger.debug("Agent id :{}, Status Updated to :{}", agentId, domainObj.getPatchscanstatus());
        return domainObj;
    }

    private String extractOsVersion(String osVersion, String sysInfo) {
        String version = osVersion;
        try {
            SysInfo systemInfo = SysInfoUtils.getSystemInfo(sysInfo);
            if (osVersion != null) {
                serviceLogger.debug("Os Version :{}", osVersion);
                osVersion = ParseUtil.getSplitVal(osVersion, " ", 0);
                int numberOfDots = FlotoStringUtils.countMatches(osVersion, ".");
                if (numberOfDots > 2) {
                    osVersion = FlotoStringUtils.substringBeforeLast(osVersion, ".");
                }
                if (systemInfo.isServerOs()) {
                    version = SysInfoUtils.getServerVersionMap().get(osVersion);
                } else {
                    version = SysInfoUtils.getClientVersionMap().get(osVersion);
                }
            }
        } catch (Exception e) {
            serviceLogger.debug("Error while extracting osversion:{}", osVersion, e);
        }
        return version;

    }

    @Override
    public void after_create(CallContext callContext, AgentRest createRequest, Agent persistedDomainModel) {
        super.after_create(callContext, createRequest, persistedDomainModel);
        synchronized (FlotoManagedModels.AGENT) {
            persistedDomainModel.setName(
                    sequenceService.getNextSequence(callContext, FlotoManagedModels.AGENT,
                            Constants.AGENT_PREFIX));
        }
        flotoZmqService.scheduleHeartBeatThreadForZmqClient();
    }

    @Override
    public void afterCreateInNonTransaction(CallContext callContext, AgentRest createRequest,
            Agent concreteModel) {
        super.afterCreateInNonTransaction(callContext, createRequest, concreteModel);
        tagService.createTagsIfNotExists(callContext, concreteModel.getTags());
        createAuditForAgentCreation(callContext, concreteModel);
    }

    @Override
    protected void afterUpdatePartialInNonTransaction(CallContext callContext, AgentRest updateRequest,
            Agent persistedModel, FlotoDiffObjMap diffObjMap) {
        super.afterUpdatePartialInNonTransaction(callContext, updateRequest, persistedModel, diffObjMap);
        if (updateRequest.containProperty(Agent_.TAGS)) {
            tagService.createTagsIfNotExists(callContext, persistedModel.getTags());
        }
    }

    public boolean checkActivationCode(CallContext callContext, long agentId, String incomingActivationCode,
            boolean mobileAgent, String incomingAgentVersion) {
        AgentSetting setting = agentSettingService.get(callContext);
        if (!setting.isOptForMoreSecureCommunication()) {
            Organization organization = organizationService.get(callContext);
            if (FlotoStringUtils.isNotBlank(incomingActivationCode)) {
                if (mobileAgent && FlotoStringUtils.equals(organization.getMobileAgentActivationCode(),
                        incomingActivationCode)) {
                    discoveryLogger.debug("Mobile Agent activation code is valid of agent id = {}", agentId);
                    return true;
                } else if (!mobileAgent && organization.getAgentActivationCode()
                        .equals(incomingActivationCode)) {
                    discoveryLogger.debug("Agent activation code is valid of agent id = {}", agentId);
                    return true;
                }
            }

            if (FlotoCoreClientKeys.AGENT_KEY.equalsIgnoreCase(callContext.getClient().getClientId())) {
                return false;
            }
            discoveryLogger.debug("Invalid Agent activation code of agent id = {}", agentId);
        }

        discoveryLogger.debug(
                "User has opted for new Communication using Credential. Skipping Activation Code validation.");
        if (FlotoStringUtils.isNotBlank(incomingAgentVersion)) {
            Version incomingVersion = new Version(incomingAgentVersion);
            if (incomingVersion.compareTo(new Version(this.validAgentVersion)) < 0) {
                discoveryLogger.debug(
                        "[FALSE] Version {} compatibility Does not Match For Agent ID={}.Required version >={}",
                        incomingVersion, agentId, validAgentVersion);
                return false;
            }
        } else {
            discoveryLogger.warn("No Version Found!");
            return false;
        }
        return true;
    }


    @Override
    public boolean checkVersionCompatibility(CallContext callContext, long agentId,
            String incomingAgentVersion, String agentActivationCode, boolean mobileagent) {
        discoveryLogger.debug("Checking Version Compatibility for Agent ID={}, Version={}", agentId,
                incomingAgentVersion);

        boolean validCode = checkActivationCode(callContext, agentId, agentActivationCode, mobileagent,
                incomingAgentVersion);
        if (!validCode) {
            return false;
        }

        if (mobileagent) {

            return true;
            // TODO SAAS-5117

        } else {
            if (FlotoStringUtils.isBlank(this.supportedAgentVersion)) {
                discoveryLogger.debug(
                        "[ERROR] seems like 'com.flotomate.support.agent.version' property is not defined or blank in property file.");
            } else {
                discoveryLogger.debug("Supported Version for this Build={} and Incoming Agent Version={}",
                        this.supportedAgentVersion, incomingAgentVersion);

                try {
                    Version incomingVersion = new Version(incomingAgentVersion);
                    if (incomingVersion.compareTo(new Version(this.supportedAgentVersion)) >= 0) {
                        discoveryLogger.debug("[TRUE] Version compatibility Match For Agent ID={}", agentId);
                        return true;
                    }
                } catch (Exception e) {
                    discoveryLogger.error("Exception while parsing agent version", e);
                }
            }
        }

        discoveryLogger.debug("[FALSE] Version compatibility does not match For Agent ID={}", agentId);
        return false;
    }

    private void createAgentCreationFailedAudit(CallContext callContext, String message,
            String uniqIndenfiers) {

        AdminAuditRest rest = new AdminAuditRest();
        String auditString = "Agent creation failed for " + uniqIndenfiers + " because " + message;
        rest.setDisplayName("Agent Creation Failed");
        rest.setAuditString(auditString);
        rest.setRefModel(FlotoManagedModels.AGENT);
        rest.setAuditEvent(AuditEvent.CREATE);
        rest.setPerformerId(callContext.getUser().getId());
        rest.setPerformerType(PerformerType.USER);
        adminAuditService.createInNewTransaction(callContext, rest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Agent handleAgentInfoOnRefreshRequest(CallContext callContext, AgentRefreshRest refreshRequest) {

        AssetLocatorQuery locatorQuery = null;
        Agent locatedAgent = null;
        try {
            if (refreshRequest.isMobileAgent()) {
                locatorQuery =
                        new AssetLocatorQuery(refreshRequest.getAgentId(), refreshRequest.getOsPlatform(),
                                FlotoStringUtils.EMPTY, FlotoStringUtils.EMPTY, refreshRequest.getIpAddress(),
                                refreshRequest.getMacAddress(), refreshRequest.getUuid(), null, 0);

                locatedAgent = agentLocatorService.locateMobileAgent(callContext, locatorQuery);
            } else {
                locatorQuery =
                        new AssetLocatorQuery(refreshRequest.getAgentId(), refreshRequest.getOsPlatform(),
                                refreshRequest.getHostName(), refreshRequest.getDomainName(),
                                refreshRequest.getIpAddress(), refreshRequest.getMacAddress(),
                                refreshRequest.getUuid(), null, 0, refreshRequest.getSerialNumber(),
                                refreshRequest.getBiosSerialNumber());
                locatedAgent = (Agent) locatorService.locateAgentForAgent(callContext, locatorQuery,
                        FlotoManagedModels.AGENT);
            }
        } catch (FlotoException e) {
            FlotoErrorResponse exceptionRest = exceptionHandler.handleException(e);
            createAgentCreationFailedAudit(callContext, exceptionRest.getUserMessage(),
                    FlotoStringUtils.isNotBlank(refreshRequest.getIpAddress()) ?
                            refreshRequest.getIpAddress() :
                            refreshRequest.getHostName());
            throw e;
        } catch (Exception e) {
            createAgentCreationFailedAudit(callContext, "Error while Creating Agent",
                    FlotoStringUtils.isNotBlank(refreshRequest.getIpAddress()) ?
                            refreshRequest.getIpAddress() :
                            refreshRequest.getHostName());
            throw e;
        }

        Agent agent;
        if (locatedAgent == null) {
            discoveryLogger.debug(
                    "Found Agent ID = 0, Seems to be Request from New Agent. Let's check same hostName exist or not.");

            discoveryLogger.debug("Creating a new agent. Host Name = {}", refreshRequest.getHostName());

            String ipAddress = refreshRequest.getIpAddress();
            if (FlotoStringUtils.isBlank(ipAddress)) {
                discoveryLogger.debug("Invalid IP Address");
                throw new FlotoException(callContext.buildCallerInfo(), FlotoErrorTemplates.INVALID_INPUTS,
                        new String[] { "IP Address" });
            }

            AgentRest agentRest = new AgentRest();
            // Validate New Agent Info
            agentRest.setIpAddress(refreshRequest.getIpAddress());
            agentRest.setHostName(refreshRequest.getHostName());
            agentRest.setAgentVersion(refreshRequest.getAgentVersion());
            agentRest.setOsName(refreshRequest.getOsName());
            agentRest.setDomainName(refreshRequest.getDomainName());
            autoIdentifyUsedByUser(callContext, refreshRequest, agentRest);
            agentRest.setServicePack(refreshRequest.getServicePack());
            agentRest.setArchitecture(refreshRequest.getArchitecture());
            agentRest.setUuid(refreshRequest.getUuid());
            agentRest.setPlatform(refreshRequest.getOsPlatform());
            agentRest.setLastRefreshCallTime(System.currentTimeMillis());
            agentRest.setLanguage(refreshRequest.getLanguage());
            agentRest.setOsVersion(refreshRequest.getOsVersion());
            agentRest.setRebootRequired(refreshRequest.isRebootRequired());
            agentRest.setLastRebootTime(refreshRequest.getLastRebootTime());
            agentRest.setSerialNumber(refreshRequest.getSerialNumber());
            agentRest.setMacAddress(refreshRequest.getMacAddress());
            agentRest.setBiosSerialNumber(refreshRequest.getBiosSerialNumber());
            agentRest.setPollerId(refreshRequest.getPollerId());
            if (callContext.getClient() != null) {
                agentRest.setCredentialProfileId(credentialProfileService.getIdByQual(callContext,
                        QualUtils.buildRelationalQual(CredentialProfile_.FLOTO_CLIENT_ID,
                                RelationalOperator.Equal, callContext.getClient().getId())));
            }
            if (OsPlatform.UNIX_RED_HAT.equals(refreshRequest.getOsPlatform())) {
                agentRest.setOsVersionNumber(refreshRequest.getOsVersion().split(" ")[0].trim());
            }

            agent = create(callContext, agentRest);

            discoveryLogger.debug("Agent has been created. Host Name = {}, ID={}, IP={}, Version={}",
                    agentRest.getHostName(), agent.getId(), agentRest.getIpAddress(),
                    agentRest.getAgentVersion());
            // If asset licence not avalible then no need to add scan now command
            boolean isAssetLicenceAvalible =
                    FlotoUtils.checkLicenseForModule(callContext, LicenseManagedModules.ASSET);
            if (isAssetLicenceAvalible) {
                discoveryLogger.debug("creating scan_now command for created Agent.");
                CommandRest commandRest = new CommandRest();
                commandRest.setAgentId(agent.getId());
                commandRest.setWorkType(RCWorkType.SCAN_NOW);
                commandRest.setState(CommandState.YET_TO_RECEIVE);
                commandService.create(callContext, commandRest);
            }

            discoveryLogger.debug("Creating config_update command if the agent setting is updated");
            CommandRest confUpdtCmd = new CommandRest();
            confUpdtCmd.setAgentId(agent.getId());
            confUpdtCmd.setWorkType(RCWorkType.UPDATE_CONFIG);
            confUpdtCmd.setState(CommandState.YET_TO_RECEIVE);
            commandService.create(callContext, confUpdtCmd);

        } else {
            // update
            discoveryLogger.debug("Updating Agent if required for agent id : {}", locatedAgent.getId());
            updateAgentInfoRequired(callContext, locatedAgent, refreshRequest);
            discoveryLogger.debug("required info updated for agent id : {}", locatedAgent.getId());
            agent = locatedAgent;
        }

        if (!refreshRequest.isMobileAgent()) {
            agentPatchCrossService.createRepoSyncCommand(callContext, agent.getId(),
                    refreshRequest.getOsPlatform());
        }

        return agent;
    }

    private void autoIdentifyUsedByUser(CallContext callContext, AgentRefreshRest refreshRequest,
            AgentRest agentRest) {
        String userLogOnName = refreshRequest.getLoggedOnUser();
        if (FlotoStringUtils.isBlank(userLogOnName)) {
            return;
        }
        agentRest.setLastLoggedInUser(userLogOnName);
        agentRest.addInPatchMap(Agent_.LAST_LOGGED_IN_USER, agentRest.getLastLoggedInUser());
        AdminValue value = tenantPreferenceService.getValue(callContext,
                TenantPreference.ASSET_CONSIDER_LAST_LOGIN_AS_USEDBY);
        if (value != null && value.isBooleanValue() && value.getBooleanValue()) {
            String[] userNames = FlotoStringUtils.split(userLogOnName, "\\");
            userLogOnName = userNames[userNames.length - 1];

            Qualification qual = QualUtils.buildRelationalQual(FlotoUser_.USER_LOG_ON_NAME,
                    RelationalOperator.Equal_Case_Insensitive, userLogOnName);

            agentRest.setLoggedOnUser(userService.getIdByQual(callContext, qual));
            agentRest.addInPatchMap(Agent_.LOGGED_ON_USER, agentRest.getLoggedOnUser());

        }
    }

    private void updateAgentInfoRequired(CallContext callContext, Agent locatedAgent,
            AgentRefreshRest refreshRequest) {
        // create object of AgentRest
        AgentRest agentRest = new AgentRest();
        super.updateAgentInfoRequired(callContext, refreshRequest, agentRest);

        agentRest.setPollerId(refreshRequest.getPollerId());
        agentRest.addInPatchMap(Agent_.POLLER_ID, agentRest.getPollerId());

        if (!FlotoStringUtils.equals(locatedAgent.getAgentVersion(), refreshRequest.getAgentVersion())) {
            CommandRest confUpdtCmd = new CommandRest();
            confUpdtCmd.setAgentId(locatedAgent.getId());
            confUpdtCmd.setWorkType(RCWorkType.UPDATE_CONFIG);
            confUpdtCmd.setState(CommandState.YET_TO_RECEIVE);
            commandService.create(callContext, confUpdtCmd);
        }
        agentRest.setAgentVersion(refreshRequest.getAgentVersion());
        agentRest.addInPatchMap(Agent_.AGENT_VERSION, agentRest.getAgentVersion());

        autoIdentifyUsedByUser(callContext, refreshRequest, agentRest);
        agentRest.setServicePack(refreshRequest.getServicePack());
        agentRest.addInPatchMap(Agent_.SERVICE_PACK, agentRest.getServicePack());

        agentRest.setLanguage(refreshRequest.getLanguage());
        agentRest.addInPatchMap(Agent_.LANGUAGE, agentRest.getLanguage());

        agentRest.setOsVersion(refreshRequest.getOsVersion());
        agentRest.addInPatchMap(Agent_.OS_VERSION, agentRest.getOsVersion());

        agentRest.setRebootRequired(refreshRequest.isRebootRequired());
        agentRest.addInPatchMap(Agent_.REBOOT_REQUIRED, agentRest.isRebootRequired());

        agentRest.setLastRebootTime(refreshRequest.getLastRebootTime());
        agentRest.addInPatchMap(Agent_.LAST_REBOOT_TIME, agentRest.getLastRebootTime());

        agentRest.setBiosSerialNumber(refreshRequest.getBiosSerialNumber());
        agentRest.addInPatchMap(Agent_.BIOS_SERIAL_NUMBER, agentRest.getBiosSerialNumber());

        if (refreshRequest.isStartUp() && locatedAgent.getPatchscanstatus() == CommandState.IN_PROGRESS) {
            agentRest.setPatchscanstatus(CommandState.YET_TO_RECEIVE);
            agentRest.addInPatchMap(Agent_.PATCHSCANSTATUS, agentRest.getPatchscanstatus());
        }

        agentRest.setRemoved(false);
        agentRest.addInPatchMap(FlotoBase_.REMOVED, agentRest.isRemoved());

        if (OsPlatform.UNIX_RED_HAT.equals(refreshRequest.getOsPlatform())) {
            agentRest.setOsVersionNumber(refreshRequest.getOsVersion().split(" ")[0].trim());
            agentRest.addInPatchMap(Agent_.OS_VERSION_NUMBER, agentRest.getOsVersionNumber());
        }
        // call update method.
        updatePartial(callContext, locatedAgent.getId(), agentRest);

    }

    @Override
    public void updateLastRefreshCallTime(CallContext callContext, long agentId) {
        Agent agent = getById(callContext, agentId, false);
        agent.setLastRefreshCallTime(System.currentTimeMillis());
        repository.save(callContext, agent);
        serviceLogger.debug("lastRefreshCallTime Updated for : {}", agent.getId());
    }

    @Override
    protected Agent performPartialUpdate(CallContext callContext, AgentRest updateRequest,
            Agent existingModel, FlotoDiffObjMap diffObjMap) {
        super.performPartialUpdate(callContext, updateRequest, existingModel, diffObjMap);

        if (updateRequest.containProperty(Agent_.POLLER_ID) && FlotoUtils.notEqualObjects(
                existingModel.getPollerId(), updateRequest.getPollerId())) {
            existingModel.setPollerId(updateRequest.getPollerId());
            serviceLogger.debug("Updating Poller ID = {} for Agent Id={}", updateRequest.getPollerId(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.AGENT_VERSION) && FlotoUtils.notEqualObjects(
                existingModel.getAgentVersion(), updateRequest.getAgentVersion())) {
            existingModel.setAgentVersion(updateRequest.getAgentVersion());
            serviceLogger.debug("Updating AgentVersion = {} for Agent Id={}", updateRequest.getAgentVersion(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.LOGGED_ON_USER) && FlotoUtils.notEqualObjects(
                existingModel.getLoggedOnUser(), updateRequest.getLoggedOnUser())) {
            existingModel.setLoggedOnUser(updateRequest.getLoggedOnUser());
            serviceLogger.debug("Updating LoggedOnUser = {} for Agent Id={}", updateRequest.getLoggedOnUser(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.SERVICE_PACK) && FlotoUtils.notEqualObjects(
                existingModel.getServicePack(), updateRequest.getServicePack())) {
            existingModel.setServicePack(updateRequest.getServicePack());
            serviceLogger.debug("Updating ServicePack = {} for Agent Id={}", updateRequest.getServicePack(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.RDP_SESSION_ID) && FlotoUtils.notEqualObjects(
                existingModel.getRdpSessionId(), updateRequest.getRdpSessionId())) {
            existingModel.setRdpSessionId(updateRequest.getRdpSessionId());
            serviceLogger.debug("Updating rdpSessionId = {} for Agent Id={}", updateRequest.getRdpSessionId(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.LAST_PATCH_SCAN_TIME) && FlotoUtils.notEqualObjects(
                existingModel.getLastPatchScanTime(), updateRequest.getLastPatchScanTime())) {
            existingModel.setLastPatchScanTime(updateRequest.getLastPatchScanTime());
            serviceLogger.debug("Updating lastPatchScanTime = {} for Agent Id={}",
                    updateRequest.getLastPatchScanTime(), existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.SYS_INFO) && FlotoUtils.notEqualObjects(
                existingModel.getSysInfo(), updateRequest.getSysInfo())) {
            existingModel.setSysInfo(updateRequest.getSysInfo());
            serviceLogger.debug("Updating SysInfo = {} for Agent Id={}", updateRequest.getSysInfo(),
                    existingModel.getId());

        }

        if (updateRequest.containProperty(Agent_.HEALTH_TYPE) && FlotoUtils.notEqualObjects(
                existingModel.getHealthType(), updateRequest.getHealthType())) {
            existingModel.setHealthType(updateRequest.getHealthType());
            serviceLogger.debug("Updating HealthType = {} for Agent Id={}", updateRequest.getHealthType(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.REMOTE_OFFICE_ID) && FlotoUtils.notEqualObjects(
                existingModel.getRemoteOfficeId(), updateRequest.getRemoteOfficeId())) {
            if (updateRequest.getRemoteOfficeId() <= 0) {
                FlotoBaseService<?, ?> service = serviceResolverService.getRelevantServiceByManagedModel(
                        FlotoManagedModels.REMOTE_OFFICE);
                if (service != null) {

                    List<Long> localOfficeId = service.getAllIdsByQual(callContext,
                            QualUtils.buildRelationalQual(FlotoBase_.OOB_TYPE, RelationalOperator.Equal,
                                    OobType.SYSTEM_SETUP_DATA), null);
                    if (CollectionUtils.isNotEmpty(localOfficeId)) {
                        existingModel.setRemoteOfficeId(localOfficeId.get(0));
                    }
                }
            } else {
                existingModel.setRemoteOfficeId(updateRequest.getRemoteOfficeId());
            }
            serviceLogger.debug("Updating RemoteOfficeId = {} for Agent Id={}",
                    updateRequest.getRemoteOfficeId(), existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.LANGUAGE) && FlotoUtils.notEqualObjects(
                existingModel.getLanguage(), updateRequest.getLanguage())) {
            existingModel.setLanguage(updateRequest.getLanguage());
            serviceLogger.debug("Updating language = {} for Agent Id={}", updateRequest.getLanguage(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.PATCH_ENABLED) && FlotoUtils.notEqualObjects(
                existingModel.isPatchEnabled(), updateRequest.isPatchEnabled())) {
            existingModel.setPatchEnabled(updateRequest.isPatchEnabled());
            serviceLogger.debug("Updating PatchEnabled = {} for Agent Id={}", updateRequest.isPatchEnabled(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.TAGS) && !FlotoUtils.compareObjects(existingModel.getTags(),
                updateRequest.getTags())) {
            existingModel.setTags(
                    FlotoUtils.doInPlaceSetUpdate(existingModel.getTags(), updateRequest.getTags()));
            serviceLogger.debug("Updating Tags = {} for Agent Id={}", updateRequest.getTags(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.OS_APP_IDS) && !FlotoUtils.compareObjects(
                existingModel.getOsAppIds(), updateRequest.getOsAppIds())) {
            existingModel.setOsAppIds(
                    FlotoUtils.doInPlaceSetUpdate(existingModel.getOsAppIds(), updateRequest.getOsAppIds()));
            serviceLogger.debug("Updating OsAppIds = {} for Agent Id={}", updateRequest.getOsAppIds(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.INSTALLED_KB_IDS) && !FlotoUtils.compareObjects(
                existingModel.getInstalledKbIds(), updateRequest.getInstalledKbIds())) {
            existingModel.setInstalledKbIds(updateRequest.getInstalledKbIds());
            serviceLogger.debug("Updating installedKbIds = {} for Agent Id={}",
                    updateRequest.getInstalledKbIds(), existingModel.getId());
        }
        if (updateRequest.containProperty(Agent_.ANTI_SPYWARE_VERSION) && !FlotoUtils.compareObjects(
                existingModel.getAntiSpywareVersion(), updateRequest.getAntiSpywareVersion())) {
            existingModel.setAntiSpywareVersion(updateRequest.getAntiSpywareVersion());
            serviceLogger.debug("Updating antiSpywareVersion = {} for Agent Id={}",
                    updateRequest.getAntiSpywareVersion(), existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.ANTI_MALWARE_VERSION) && !FlotoUtils.compareObjects(
                existingModel.getAntiMalwareVersion(), updateRequest.getAntiMalwareVersion())) {
            existingModel.setAntiMalwareVersion(updateRequest.getAntiMalwareVersion());
            serviceLogger.debug("Updating getAntiMalwareVersion = {} for Agent Id={}",
                    updateRequest.getAntiMalwareVersion(), existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.MRT_VERSION) && FlotoUtils.notEqualObjects(
                existingModel.getMrtVersion(), updateRequest.getMrtVersion())) {
            existingModel.setMrtVersion(updateRequest.getMrtVersion());
            serviceLogger.debug("Updating mrtVersion = {} for Agent Id={}", updateRequest.getMrtVersion(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.REBOOT_REQUIRED) && FlotoUtils.notEqualObjects(
                existingModel.isRebootRequired(), updateRequest.isRebootRequired())) {
            existingModel.setRebootRequired(updateRequest.isRebootRequired());
            serviceLogger.debug("Updating rebootRequired = {} for Agent Id={}",
                    updateRequest.isRebootRequired(), existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.OS_VERSION) && FlotoUtils.notEqualObjects(
                existingModel.getOsVersion(), updateRequest.getOsVersion())) {
            String osVersion = extractOsVersion(updateRequest.getOsVersion(), existingModel.getOsVersion());
            existingModel.setOsVersion(osVersion);
            serviceLogger.debug("Updating osVersion = {} for Agent Id={}", updateRequest.getOsVersion(),
                    existingModel.getId());
        }
        if (updateRequest.containProperty(Agent_.OS_VARIANT) && FlotoUtils.notEqualObjects(
                existingModel.getOsVariant(), updateRequest.getOsVariant())) {
            existingModel.setOsVariant(updateRequest.getOsVariant());
            serviceLogger.debug("Updating osVariant = {} for Agent Id={}", updateRequest.getOsVariant(),
                    existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.BIOS_SERIAL_NUMBER) && FlotoUtils.notEqualObjects(
                existingModel.getBiosSerialNumber(), updateRequest.getBiosSerialNumber())) {
            existingModel.setBiosSerialNumber(updateRequest.getBiosSerialNumber());
            serviceLogger.debug("Updating " + Agent_.BIOS_SERIAL_NUMBER + " = {} for Agent Id={}",
                    updateRequest.getBiosSerialNumber(), existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.PATCHSCANSTATUS) && FlotoUtils.notEqualObjects(
                existingModel.getPatchscanstatus(), updateRequest.getPatchscanstatus())) {
            existingModel.setPatchscanstatus(updateRequest.getPatchscanstatus());
            serviceLogger.debug("Updating patchScanStatus = {} for Agent Id={}",
                    updateRequest.getPatchscanstatus(), existingModel.getId());
        }

        if (updateRequest.containProperty(Agent_.LAST_REBOOT_TIME) && FlotoUtils.notEqualObjects(
                existingModel.getLastRebootTime(), updateRequest.getLastRebootTime())) {
            existingModel.setLastRebootTime(updateRequest.getLastRebootTime());
            LOGGER.addUpdateLog(callContext.getUser().getId(), FlotoManagedModels.AGENT.getName(),
                    existingModel.getId(), Agent_.LAST_REBOOT_TIME, updateRequest.getLastRebootTime());
        }

        if (updateRequest.containProperty(Agent_.OS_VERSION_NUMBER) && FlotoUtils.notEqualObjects(
                existingModel.getOsVersionNumber(), updateRequest.getOsVersionNumber())) {
            existingModel.setOsVersionNumber(updateRequest.getOsVersionNumber());
            LOGGER.addUpdateLog(callContext.getUser().getId(), FlotoManagedModels.AGENT.getName(),
                    existingModel.getId(), Agent_.OS_VERSION_NUMBER, updateRequest.getOsVersionNumber());
        }
        if (isPropertyUpdated(updateRequest, Agent_.LAST_LOGGED_IN_USER, existingModel.getLastLoggedInUser(),
                updateRequest.getLastLoggedInUser())) {
            existingModel.setLastLoggedInUser(updateRequest.getLastLoggedInUser());
            LOGGER.addUpdateLog(callContext.getUser().getId(), FlotoManagedModels.AGENT.getName(),
                    existingModel.getId(), Agent_.LAST_LOGGED_IN_USER, updateRequest.getLastLoggedInUser());
        }

        return existingModel;
    }

    @Override
    public Agent getByUuid(CallContext callContext, String uuid, OsPlatform osPlatform) {
        Qualification uuidQual =
                QualUtils.buildRelationalQual(AgentBase_.UUID, RelationalOperator.Equal_Case_Insensitive,
                        uuid);
        Agent agent = null;
        // TODO: Memory leak
        List<Agent> list = searchByQualification(callContext, uuidQual, null);
        if (CollectionUtils.isNotEmpty(list)) {
            agent = list.get(0);
            if (list.size() > 1) {
                for (Agent singleAgent : list) {
                    if (singleAgent.getPlatform() == osPlatform) {
                        agent = singleAgent;
                        break;
                    }
                }
            }
        }
        return agent;
    }

    @Override
    public Agent getByAssetId(CallContext callContext, long assetId, boolean includeArchived) {
        Qualification qual =
                QualUtils.buildRelationalQual(Agent_.ASSET_ID, RelationalOperator.Equal, assetId);
        return searchFirstByQualification(callContext, qual);
    }

    @Override
    protected Agent getDomainModel(AgentRest createRequest) {
        return new Agent();
    }

    @Override
    public Agent updateComputerDetails(CallContext callContext, long agentId, AgentDetailsRest rest) {
        Agent agent = getById(callContext, agentId, false);
        if (agent != null) {
            serviceLogger.debug("Updating computer details of agent id = {}", agentId);
            AgentRest agentRest = new AgentRest();
            agentRest.setTags(rest.getTags());
            agentRest.addInPatchMap(Agent_.TAGS, agentRest.getTags());

            if (rest.getRemoteOfficeId() > 0) {
                agentRest.setRemoteOfficeId(rest.getRemoteOfficeId());
                agentRest.addInPatchMap(Agent_.REMOTE_OFFICE_ID, agentRest.getRemoteOfficeId());
            }
            agent = updatePartial(callContext, agentId, agentRest);
            serviceLogger.debug("Updated computer details");
        }
        return agent;
    }

    @Override
    public boolean submitDiscoveredPatchesInQueue(CallContext callContext,
            ScannedPatchDataRest discoveredData) {

        Agent computerAgent = getById(callContext, discoveredData.getAgentId(), false);
        if (computerAgent == null) {
            discoveryLogger.warn("Agent not exist with ID={}, HostName={}, IPAddress={}.",
                    discoveredData.getAgentId(), discoveredData.getHostName(), discoveredData.getIpAddress());
            return false;
        }

        boolean isCompatible = checkVersionCompatibility(callContext, discoveredData.getAgentId(),
                discoveredData.getAgentVersion(), discoveredData.getAgentActivationCode(),
                discoveredData.isMobileAgent());

        if (!isCompatible) {
            discoveryLogger.warn(
                    "Agent is not compatible with the server version. will not submit scanned patch data into the queue");
            return false;
        }

        if (computerAgent.isPatchEnabled()) {
            AgentScannedPatchDataEventMessage msg = new AgentScannedPatchDataEventMessage(callContext);
            msg.setPatchData(discoveredData);
            NsqProducerUtil.sendMessage(FlotoMessageDestinations.AGENT_SCANNED_PATCH_DATA_QUEUE, msg);
            discoveryLogger.info("Patch data submited in queue for agent : {}", computerAgent.getId());

            // update agent last refresh call time
            updateLastRefreshCallTime(callContext, computerAgent.getId());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePatchDataToAgent(CallContext callContext, long agentId,
            ScannedPatchDataRest discoveredData) {
        Agent agent = getById(callContext, agentId, false);
        if (agent != null && agent.isPatchEnabled()) {
            parsedPatchSupportedDataAndUpdateAgent(callContext, agentId, discoveredData, agent);
        } else {
            serviceLogger.warn("[savePatchDataToAgent] No agent found having id={}", agentId);
        }
    }

    private void parsedPatchSupportedDataAndUpdateAgent(CallContext callContext, long agentId,
            ScannedPatchDataRest discoveredData, Agent agent) {
        createOsApplicationForOsName(callContext, agent);
        String sysInfoOutput = null;
        Map<String, Object> patchData = discoveredData.getPatchData();
        if (patchData != null) {
            AgentRest agentRest = new AgentRest();
            Set<String> newlyFetchedKbs = new HashSet<>();
            if (patchData.containsKey(PatchConstants.CLASS_SYS_INFO)) {
                Object sysInfoAsObj = patchData.get(PatchConstants.CLASS_SYS_INFO);
                if (sysInfoAsObj instanceof String sysInfo) {
                    sysInfoOutput = sysInfo;
                }
                serviceLogger.debug("Sysinfo old:{}, new:{}", agent.getSysInfo(), sysInfoOutput);
                if (FlotoStringUtils.equals(sysInfoOutput, agent.getSysInfo())) {
                    serviceLogger.debug("SysInfo is not changed, not updating the sysinfo");
                } else if (FlotoStringUtils.isNotBlank(sysInfoOutput)) {
                    serviceLogger.debug("Updating sysInfo");
                    agentRest.setSysInfo(sysInfoOutput);
                    agentRest.addInPatchMap("sysInfo", agentRest.getSysInfo());
                    serviceLogger.debug("Updated sysInfo of agent id={}", agentId);
                }
                newlyFetchedKbs.addAll(extractAllKbs(sysInfoOutput));
            }
            if (patchData.containsKey(PatchConstants.CLASS_REG_PATCHES)) {
                Object regInstalledPatches = patchData.get(PatchConstants.CLASS_REG_PATCHES);
                if (regInstalledPatches instanceof Map) {
                    newlyFetchedKbs.addAll(extractAllKbs(regInstalledPatches.toString()));
                }
            }
            if (patchData.containsKey(PatchConstants.CLASS_WUA_PACTCHES)) {
                Object wuaInstalledPatches = patchData.get(PatchConstants.CLASS_WUA_PACTCHES);
                if (wuaInstalledPatches instanceof String wuaInstalledPatchesStr) {
                    newlyFetchedKbs.addAll(extractAllKbs(wuaInstalledPatchesStr));
                }
            }
            // Process Office 2016 and 2019 data
            processMicrosoftOfficeData(callContext, agentId, patchData);

            if (patchData.containsKey(PatchConstants.CLASS_EDGE_INFO_64) || patchData.containsKey(
                    PatchConstants.CLASS_EDGE_INFO_32)) {
                Object edgeInfo = patchData.get(PatchConstants.CLASS_EDGE_INFO_64);
                Object edgeInfo32 = patchData.get(PatchConstants.CLASS_EDGE_INFO_32);
                Object edgeInfo64Hkcu = patchData.get(PatchConstants.CLASS_EDGE_INFO_64_HKCU);
                Object edgeInfo32Hkcu = patchData.get(PatchConstants.CLASS_EDGE_INFO_32_HKCU);

                boolean edgeDetected = false;
                String appName = PatchConstants.WINDOWS_EDGE_APP;
                if ((edgeInfo instanceof String edgeInfoValue && FlotoStringUtils.isNotBlank(edgeInfoValue))
                        || (edgeInfo32 instanceof String edgeInfo32Value && FlotoStringUtils.isNotBlank(
                        edgeInfo32Value)) || (edgeInfo64Hkcu instanceof String edgeInfo64HkcuValue
                        && FlotoStringUtils.isNotBlank(edgeInfo64HkcuValue)) || (
                        edgeInfo32Hkcu instanceof String edgeInfo32HkcuValue && FlotoStringUtils.isNotBlank(
                                edgeInfo32HkcuValue))) {
                    String edgeString = edgeInfo + (String) edgeInfo32 + edgeInfo64Hkcu + edgeInfo32Hkcu;
                    serviceLogger.debug("Edge Info :{}", edgeString);
                    if (FlotoStringUtils.containsIgnoreCase(edgeString,
                            PatchConstants.WINDOWS_EDGE_STABLE_KEY)) {
                        edgeDetected = true;
                        appName = PatchConstants.WINDOWS_EDGE_STABLE_APP;
                        serviceLogger.debug("Stable Edge detected in agent id :{}", agentId);
                        createOrDeleteApplicationAndAgentRelation(callContext, agentId, appName,
                                edgeDetected);
                    } else {
                        serviceLogger.debug("Stable Edge not detected in agent id :{}", agentId);
                        createOrDeleteApplicationAndAgentRelation(callContext, agentId,
                                PatchConstants.WINDOWS_EDGE_STABLE_APP, false);
                    }

                    if (FlotoStringUtils.containsIgnoreCase(edgeString,
                            PatchConstants.WINDOWS_EDGE_DEV_KEY)) {
                        edgeDetected = true;
                        appName = PatchConstants.WINDOWS_EDGE_DEV_APP;
                        serviceLogger.debug("Dev Edge detected in agent id :{}", agentId);
                        createOrDeleteApplicationAndAgentRelation(callContext, agentId, appName,
                                edgeDetected);
                    } else {
                        serviceLogger.debug("Dev Edge not detected in agent id :{}", agentId);
                        createOrDeleteApplicationAndAgentRelation(callContext, agentId,
                                PatchConstants.WINDOWS_EDGE_DEV_APP, false);
                    }

                    if (FlotoStringUtils.containsIgnoreCase(edgeString,
                            PatchConstants.WINDOWS_EDGE_BETA_KEY)) {
                        edgeDetected = true;
                        appName = PatchConstants.WINDOWS_EDGE_BETA_APP;
                        serviceLogger.debug("Beta Edge detected in agent id :{}", agentId);
                        createOrDeleteApplicationAndAgentRelation(callContext, agentId, appName,
                                edgeDetected);
                    } else {
                        serviceLogger.debug("Beta Edge not detected in agent id :{}", agentId);
                        createOrDeleteApplicationAndAgentRelation(callContext, agentId,
                                PatchConstants.WINDOWS_EDGE_BETA_APP, false);
                    }

                    if (FlotoStringUtils.containsIgnoreCase(edgeString,
                            PatchConstants.WINDOWS_EDGE_CANARY_KEY)) {
                        edgeDetected = true;
                        appName = PatchConstants.WINDOWS_EDGE_CANARY_APP;
                        serviceLogger.debug("Canary Edge detected in agent id :{}", agentId);
                        createOrDeleteApplicationAndAgentRelation(callContext, agentId, appName,
                                edgeDetected);
                    } else {
                        serviceLogger.debug("Canary Edge not detected in agent id :{}", agentId);
                        createOrDeleteApplicationAndAgentRelation(callContext, agentId,
                                PatchConstants.WINDOWS_EDGE_CANARY_APP, false);
                    }

                }
                if (!edgeDetected) {
                    serviceLogger.debug("Edge Not detected so removing relation in agent id :{}", agentId);
                    appName = PatchConstants.WINDOWS_EDGE_APP;
                    createOrDeleteApplicationAndAgentRelation(callContext, agentId, appName, edgeDetected);
                }
            }
            if (patchData.containsKey(PatchConstants.CLASS_MPCOMPUTERSTATUS_INFO)) {
                Object defenderCommandResult = patchData.get(PatchConstants.CLASS_MPCOMPUTERSTATUS_INFO);
                if (defenderCommandResult instanceof String defenderInfo) {
                    String[] propertyList = defenderInfo.split("\n");
                    if (ArrayUtils.isNotEmpty(propertyList)) {
                        for (String prop : propertyList) {
                            String fieldName = ParseUtil.getSplitVal(prop, ":", 0).trim().toLowerCase();
                            String fieldValue = ParseUtil.getSplitVal(prop, ":", 1).trim();
                            switch (fieldName) {
                                case PatchConstants.AMPRODUCTVERSION -> {
                                    agentRest.setAntiMalwareVersion(fieldValue);
                                    agentRest.addInPatchMap(Agent_.ANTI_MALWARE_VERSION, fieldValue);
                                }
                                default -> {
                                    // handle for other field if required
                                }
                            }
                        }
                    }
                }
            }
            if (patchData.containsKey(PatchConstants.CLASS_DEFENDER_INFO) || patchData.containsKey(
                    PatchConstants.CLASS_DEFENDER_POLICY_INFO)) {

                boolean isDefenderUpdateDisableByPolicy = isDefenderUpdateDisableByPolicy(
                        patchData.get(PatchConstants.CLASS_DEFENDER_POLICY_INFO));

                boolean isDefenderUPdateDisableByUser =
                        isDefenderUpdateDisableByUser(patchData.get(PatchConstants.CLASS_DEFENDER_INFO));
                createOrDeleteApplicationAndAgentRelation(callContext, agentId,
                        PatchConstants.WINDOWS_DEFENDER_APP,
                        !(isDefenderUpdateDisableByPolicy || isDefenderUPdateDisableByUser));
                String antispywareversion =
                        getAntiSpywareVersion(patchData.get(PatchConstants.CLASS_DEFENDER_INFO));

                agentRest.setAntiSpywareVersion(antispywareversion);
                agentRest.addInPatchMap(Agent_.ANTI_SPYWARE_VERSION, antispywareversion);

            }
            if (patchData.containsKey(PatchConstants.CLASS_MRT_VERSION_INFO)) {
                Object mrtVersionInfo = patchData.get(PatchConstants.CLASS_MRT_VERSION_INFO);
                if (mrtVersionInfo instanceof String mrtVersionInfoStr) {
                    String mrtVersion = ParseUtil.getSplitVal(mrtVersionInfoStr, "=", 1);
                    mrtVersion = FlotoStringUtils.trim(mrtVersion);
                    agentRest.setMrtVersion(mrtVersion);
                    agentRest.addInPatchMap(Agent_.MRT_VERSION, mrtVersion);
                }
            }

            serviceLogger.debug("Newly fetched installed kb ids={}", newlyFetchedKbs);
            Set<String> existingKbIds = Sets.newHashSet(
                    FlotoStringUtils.split(agent.getInstalledKbIds() != null ? agent.getInstalledKbIds() : "",
                            ","));
            serviceLogger.debug("Existing installed kb ids ={}", existingKbIds);
            if (!CollectionUtils.isEqualCollection(existingKbIds, newlyFetchedKbs)
                    && CollectionUtils.isNotEmpty(newlyFetchedKbs)) {
                Collection<String> newItems = CollectionUtils.subtract(newlyFetchedKbs, existingKbIds);
                serviceLogger.debug("Found new kb ids = {}", newItems);
                Collection<String> removedItems = CollectionUtils.subtract(existingKbIds, newlyFetchedKbs);
                serviceLogger.debug("Found removed kb ids ={}", removedItems);
                existingKbIds.addAll(newItems);
                existingKbIds.removeAll(removedItems);
                String updatedKbIds = StringUtils.join(existingKbIds, ",");
                serviceLogger.debug("Final installed kb ids = {}", updatedKbIds);
                agentRest.setInstalledKbIds(updatedKbIds);
                agentRest.addInPatchMap(Agent_.INSTALLED_KB_IDS, agentRest.getInstalledKbIds());
            }
            agentRest.setLastPatchScanTime(System.currentTimeMillis());
            agentRest.addInPatchMap(Agent_.LAST_PATCH_SCAN_TIME, agentRest.getLastPatchScanTime());
            serviceResolverService.getRelevantServiceByManagedModel(FlotoManagedModels.AGENT)
                    .updatePartialInNewTransaction(callContext, agentId, agentRest);
        }
    }

    private void processMicrosoftOfficeData(CallContext callContext, long agentId,
            Map<String, Object> patchData) {
        if (patchData.containsKey(PatchConstants.CLASS_OFFICE_2016) || patchData.containsKey(
                PatchConstants.CLASS_OFFICE_2019)) {
            Object office2016Info = patchData.get(PatchConstants.CLASS_OFFICE_2016);
            Object office2019Info = patchData.get(PatchConstants.CLASS_OFFICE_2019);
            String appName = "";
            boolean isOffice2016Detected = false;
            if (office2016Info instanceof String && FlotoStringUtils.containsIgnoreCase(
                    office2016Info.toString(), PatchConstants.WINDOWS_OFFICE_2016_KEY)) {
                appName = PatchConstants.WINDOWS_OFFICE_2016_APP;
                isOffice2016Detected = true;
                createOrDeleteApplicationAndAgentRelation(callContext, agentId, appName,
                        isOffice2016Detected);
            } else {
                appName = PatchConstants.WINDOWS_OFFICE_2016_APP;
                createOrDeleteApplicationAndAgentRelation(callContext, agentId, appName,
                        isOffice2016Detected);
            }
            if (!isOffice2016Detected) {
                boolean office2019Detected = parseOffice2019Data(callContext, agentId, office2019Info);

                appName = PatchConstants.WINDOWS_OFFICE_2019_APP;
                serviceLogger.debug(
                        "[processScannedPatchData] Removing Office 2019 relation for agent id: {}", agentId);
                createOrDeleteApplicationAndAgentRelation(callContext, agentId, appName,
                        isOffice2016Detected);
                if (!office2019Detected) {
                    deleteAgentApplication(callContext, agentId);
                }
            }
        }
    }

    private void deleteAgentApplication(CallContext callContext, long agentId) {
        Qualification agentIdQual = QualUtils.refIdQual(agentId);
        Qualification appTypeQual = QualUtils.buildRelationalQual(AgentApplicationConfig_.APPLICATION_TYPE,
                RelationalOperator.Equal, ApplicationType.MICROSOFT_OFFICE);

        List<AgentApplicationConfig> existingConfigs =
                agentApplicationConfigService.searchByQualification(callContext,
                        QualUtils.andQual(agentIdQual, appTypeQual), null);
        serviceLogger.debug(
                "[processScannedPatchData] Found {} Office application configs to delete for agent id: {}",
                existingConfigs.size(), agentId);

        if (CollectionUtils.isNotEmpty(existingConfigs)) {
            for (AgentApplicationConfig config : existingConfigs) {
                serviceLogger.debug(
                        "[processScannedPatchData] Deleting Office application config with id: {} for agent id: {}",
                        config.getId(), agentId);
                agentApplicationConfigService.deleteById(callContext, config.getId());
                serviceLogger.info(
                        "[processScannedPatchData] Successfully deleted Office application config with id: {} for agent id: {}",
                        config.getId(), agentId);
            }
        }
    }

    private boolean parseOffice2019Data(CallContext callContext, long agentId, Object office2019Info) {
        serviceLogger.info("[parseOffice2019Data] Processing Office 2019 data for agent id: {}", agentId);

        if (!(office2019Info instanceof String office2019InfoStr) || FlotoStringUtils.isBlank(
                office2019InfoStr)) {
            serviceLogger.debug("[parseOffice2019Data] Invalid Office 2019 info for agent id: {}", agentId);
            return false;
        }

        try {
            // Parse and clean JSON data
            Map<String, Object> configData = parseOffice2019Json(office2019InfoStr);
            if (MapUtils.isEmpty(configData)) {
                return false;
            }

            // Extract only required fields
            Map<String, Object> filteredData = filterOffice2019Data(configData);
            if (MapUtils.isEmpty(filteredData)) {
                serviceLogger.info(
                        "[parseOffice2019Data] No relevant Office 2019 data found for agent id: {}", agentId);
                return false;
            }

            // Create or update application config
            updateOfficeApplicationConfig(callContext, agentId, filteredData);

            // Create application relation
            String appName = PatchConstants.WINDOWS_OFFICE_2019_APP;
            createOrDeleteApplicationAndAgentRelation(callContext, agentId, appName, true);

            serviceLogger.info("[parseOffice2019Data] Successfully processed Office 2019 for agent id: {}",
                    agentId);
            return true;
        } catch (Exception e) {
            serviceLogger.error("[parseOffice2019Data] Error processing Office 2019 for agent id: {}",
                    agentId, e);
            return false;
        }
    }

    private Map<String, Object> parseOffice2019Json(String jsonStr) {
        Map<String, Object> fullData = new HashMap<>();
        try {
            // Clean up the JSON string if needed
            if (FlotoStringUtils.startsWith(jsonStr, "\"")) {
                jsonStr = FlotoStringUtils.removeStart(jsonStr, "\"");
            }
            if (FlotoStringUtils.endsWith(jsonStr, "\"")) {
                jsonStr = FlotoStringUtils.removeEnd(jsonStr, "\"");
            }

            ObjectMapper mapper = new ObjectMapper();
            fullData = mapper.readValue(jsonStr, Map.class);

            if (MapUtils.isEmpty(fullData) || !fullData.containsKey("Configuration")) {
                serviceLogger.debug("[parseOffice2019Json] Missing or empty Configuration data");
                return fullData;
            }

            return (Map<String, Object>) fullData.get("Configuration");
        } catch (JsonProcessingException e) {
            serviceLogger.error("[parseOffice2019Json] Error parsing JSON data", e);
            return fullData;
        }
    }

    private void updateOfficeApplicationConfig(CallContext callContext, long agentId,
            Map<String, Object> filteredData) {
        AgentApplicationConfigRest configRest = new AgentApplicationConfigRest();
        configRest.setRefId(agentId);
        configRest.setApplicationType(ApplicationType.MICROSOFT_OFFICE);
        configRest.setApplicationData(filteredData);

        // Check if config already exists
        Qualification qual = QualUtils.andQual(QualUtils.refIdQual(agentId),
                QualUtils.buildRelationalQual(AgentApplicationConfig_.APPLICATION_TYPE,
                        RelationalOperator.Equal, ApplicationType.MICROSOFT_OFFICE));

        AgentApplicationConfig existingConfig =
                agentApplicationConfigService.searchFirstByQualification(callContext, qual);

        if (existingConfig == null) {
            agentApplicationConfigService.create(callContext, configRest);
            serviceLogger.info("[updateOfficeApplicationConfig] Created new Office config for agent id: {}",
                    agentId);
        } else {
            configRest.addInPatchMap(AgentApplicationConfig_.APPLICATION_DATA, filteredData);
            agentApplicationConfigService.updatePartial(callContext, existingConfig.getId(), configRest);
            serviceLogger.info("[updateOfficeApplicationConfig] Updated Office config for agent id: {}",
                    agentId);
        }
    }

    private static Map<String, Object> filterOffice2019Data(Map<String, Object> configData) {
        Map<String, Object> filteredData = new HashMap<>();

        // 1. Office Architecture (platform)
        Object platform = configData.get("platform");
        if (ObjectUtils.isNotEmpty(platform)) {
            filteredData.put("arch", platform);
            serviceLogger.debug("[parseOffice2019Data] Found platform: {}", platform);
        }

        // 2. CDN Base URL
        Object cdnBaseUrl = configData.get("cdnbaseurl");
        if (ObjectUtils.isNotEmpty(cdnBaseUrl)) {
            filteredData.put("cdnbaseurl", cdnBaseUrl);
            serviceLogger.debug("[parseOffice2019Data] Found CDN base URL: {}", cdnBaseUrl);
        }

        Object updateChannel = configData.get("updatechannel");
        if (ObjectUtils.isNotEmpty(updateChannel)) {
            filteredData.put("updatechannel", updateChannel);
            serviceLogger.debug("[parseOffice2019Data] Found update channel : {}", updateChannel);
        }

        // 3. Client Culture/language
        Object clientCulture = configData.get("clientculture");
        if (ObjectUtils.isNotEmpty(clientCulture)) {
            filteredData.put("language", clientCulture);
            serviceLogger.debug("[parseOffice2019Data] Found client culture: {}", clientCulture);
        }

        // 4. Excluded Apps list
        Object excludedApps = configData.get("o365businessretail.excludedapps");
        if (ObjectUtils.isNotEmpty(excludedApps)) {
            filteredData.put("excludedapps", excludedApps);
            serviceLogger.debug("[parseOffice2019Data] Found excluded apps: {}", excludedApps);
        }

        // 5. ProductReleaseIds
        Object productReleaseIds = configData.get("productreleaseids");
        if (ObjectUtils.isNotEmpty(productReleaseIds)) {
            filteredData.put("productreleaseids", productReleaseIds);
            serviceLogger.debug("[parseOffice2019Data] Found product release IDs: {}", productReleaseIds);
        }

        // 6. Version information with fallback logic
        String versionInfo = null;
        if (configData.containsKey("clientversiontoreport")) {
            versionInfo = (String) configData.get("clientversiontoreport");
            serviceLogger.debug("[parseOffice2019Data] Found version from clientversiontoreport: {}",
                    versionInfo);
        } else if (configData.containsKey("clientxnoneversion")) {
            versionInfo = (String) configData.get("clientxnoneversion");
            serviceLogger.debug("[parseOffice2019Data] Found version from clientxnoneversion: {}",
                    versionInfo);
        } else if (configData.containsKey("versiontoreport")) {
            versionInfo = (String) configData.get("versiontoreport");
            serviceLogger.debug("[parseOffice2019Data] Found version from versiontoreport: {}", versionInfo);
        } else {
            serviceLogger.debug("[parseOffice2019Data] No version information found");
        }

        if (versionInfo != null) {
            filteredData.put("version", versionInfo);
        }
        return filteredData;
    }

    private String getAntiSpywareVersion(Object classDefenderInfo) {
        String assignatureversion = null;
        Map<String, Object> map = null;
        if (classDefenderInfo instanceof String data) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                if (FlotoStringUtils.startsWith(data, "\"")) {
                    data = FlotoStringUtils.removeStart(data, "\"");
                }
                if (FlotoStringUtils.endsWith(data, "\"")) {
                    data = FlotoStringUtils.removeEnd(data, "\"");
                }
                map = mapper.readValue(data, Map.class);
            } catch (JsonMappingException e) {
                serviceLogger.error(
                        "[getAntiSpywareVersion] [JsonMappingException] Error in parsing Registry data for defender:",
                        e);
            } catch (JsonProcessingException e) {
                serviceLogger.error(
                        "[getAntiSpywareVersion] [JsonProcessingException] Error in parsing Registry data for defender:",
                        e);
            }

            if (MapUtils.isNotEmpty(map) && map.containsKey("Signature Updates")) {
                Object winDef = map.get("Signature Updates");
                if (winDef instanceof Map) {
                    Map<String, String> windDefMap = (Map<String, String>) winDef;
                    if (windDefMap.containsKey("assignatureversion")) {
                        assignatureversion = windDefMap.get("assignatureversion");
                    }
                }
            }

        }
        return assignatureversion;
    }

    private boolean isDefenderUpdateDisableByUser(Object classDefenderInfo) {
        boolean isDefenderUpdateDisableByUser = false;
        boolean DisableAntiSpyware = false;
        boolean DisableAntiVirus = false;
        Map<String, Object> map = null;
        if (classDefenderInfo instanceof String data) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                if (FlotoStringUtils.startsWith(data, "\"")) {
                    data = FlotoStringUtils.removeStart(data, "\"");
                }
                if (FlotoStringUtils.endsWith(data, "\"")) {
                    data = FlotoStringUtils.removeEnd(data, "\"");
                }
                map = mapper.readValue(data, Map.class);
            } catch (JsonMappingException e) {
                serviceLogger.error(
                        "[isDefenderUpdateDisableByUser] [JsonMappingException] Error in parsing Registry data for defender:",
                        e);
            } catch (JsonProcessingException e) {
                serviceLogger.error(
                        "[isDefenderUpdateDisableByUser] [JsonProcessingException] Error in parsing Registry data for defender:",
                        e);
            }

            if (MapUtils.isNotEmpty(map) && map.containsKey(PatchConstants.WINDOWS_DEFENDER)) {
                Object winDef = map.get(PatchConstants.WINDOWS_DEFENDER);
                if (winDef instanceof Map) {
                    Map<String, String> windDefMap = (Map<String, String>) winDef;
                    if (windDefMap.containsKey(PatchConstants.DISABLEANTISPYWARE)) {
                        String disableantispyware = windDefMap.get(PatchConstants.DISABLEANTISPYWARE);
                        int bit = Integer.parseInt(disableantispyware);
                        if (bit >= 1) {
                            DisableAntiSpyware = true;
                        }
                    }
                    if (windDefMap.containsKey(PatchConstants.DISABLEANTIVIRUS)) {
                        String disableantivirus = windDefMap.get(PatchConstants.DISABLEANTIVIRUS);
                        int bit = Integer.parseInt(disableantivirus);
                        if (bit >= 1) {
                            DisableAntiVirus = true;
                        }
                    }
                }
            }

        }
        if (DisableAntiVirus || DisableAntiSpyware) {
            isDefenderUpdateDisableByUser = true;
        }
        return isDefenderUpdateDisableByUser;
    }

    private boolean isDefenderUpdateDisableByPolicy(Object classDefenderPolicyInfo) {
        boolean isDefenderUpdateDisableByPolicy = false;
        boolean DisableAntiSpyware = false;
        boolean DisableAntiVirus = false;
        Map<String, Object> map = null;
        if (classDefenderPolicyInfo instanceof String data) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                if (FlotoStringUtils.startsWith(data, "\"")) {
                    data = FlotoStringUtils.removeStart(data, "\"");
                }
                if (FlotoStringUtils.endsWith(data, "\"")) {
                    data = FlotoStringUtils.removeEnd(data, "\"");
                }
                map = mapper.readValue(data, Map.class);
            } catch (JsonMappingException e) {
                serviceLogger.error(
                        "[isDefenderUpdateDisableByPolicy] [JsonMappingException] Error in parsing Registry data for defender:",
                        e);
            } catch (JsonProcessingException e) {
                serviceLogger.error(
                        "[isDefenderUpdateDisableByPolicy] [JsonProcessingException] Error in parsing Registry data for defender:",
                        e);
            }

            if (MapUtils.isNotEmpty(map) && map.containsKey(PatchConstants.WINDOWS_DEFENDER)) {
                Object winDef = map.get(PatchConstants.WINDOWS_DEFENDER);
                if (winDef instanceof Map) {
                    Map<String, String> windDefMap = (Map<String, String>) winDef;
                    if (windDefMap.containsKey(PatchConstants.DISABLEANTISPYWARE)) {
                        Object disableAntiSpywareValue = windDefMap.get(PatchConstants.DISABLEANTISPYWARE);
                        if (disableAntiSpywareValue instanceof String disableAntiSpywareValueStr) {
                            int bit1 = Integer.parseInt(disableAntiSpywareValueStr);
                            if (bit1 >= 1) {
                                DisableAntiSpyware = true;
                            }
                        }
                    }
                    if (windDefMap.containsKey(PatchConstants.DISABLEANTIVIRUS)) {
                        Object disableAntiVirusValue = windDefMap.get(PatchConstants.DISABLEANTIVIRUS);
                        if (disableAntiVirusValue instanceof String disableAntiVirusValueStr) {
                            int bit1 = Integer.parseInt(disableAntiVirusValueStr);
                            if (bit1 >= 1) {
                                DisableAntiVirus = true;
                            }
                        }
                    }

                }
            }

        }
        if (DisableAntiVirus || DisableAntiSpyware) {
            isDefenderUpdateDisableByPolicy = true;
        }
        return isDefenderUpdateDisableByPolicy;
    }

    /**
     * @param callContext
     * @param agentId
     * @param applicationName
     * @param isCreated       Please note that the below method is used only to process the windows component. If you
     *                        want to process the linux os application please create another method.
     */
    private void createOrDeleteApplicationAndAgentRelation(CallContext callContext, long agentId,
            String applicationName, boolean isCreated) {
        FlotoBaseService<OsApplication, OsApplicationRest> osAppService =
                serviceResolverService.getRelevantServiceByManagedModel(FlotoManagedModels.OSAPPLICATION);
        FlotoBaseService<AgentApplication, AgentApplicationRest> agentAppService =
                serviceResolverService.getRelevantServiceByManagedModel(FlotoManagedModels.AGENT_APPLICATION);

        if (isCreated) {
            // Create OS Application and add relation to Agent-Application
            if (applicationName != null) {
                OsApplication osApp = osAppService.getByNameIgnoreCase(callContext, applicationName);
                if (osApp == null) {
                    OsApplicationRest osAppRest = new OsApplicationRest();
                    osAppRest.setProductType(ProductType.APPLICATION);
                    osAppRest.setPlatform(OsPlatform.WINDOWS);
                    osAppRest.setName(applicationName);
                    osAppRest.setHidden(true);
                    osApp = osAppService.createInNewTransaction(callContext, osAppRest);
                    serviceLogger.debug("Created osApp name = {}, id ={}", osApp.getName(), osApp.getId());
                }
                Qualification agentIdQual =
                        QualUtils.buildRelationalQual(AgentApplication_.AGENT_ID, RelationalOperator.Equal,
                                agentId);
                Qualification productIdQual =
                        QualUtils.buildRelationalQual(AgentApplication_.PRODUCT_ID, RelationalOperator.Equal,
                                osApp.getId());
                long count = agentAppService.getCountByQual(callContext,
                        QualUtils.andQual(agentIdQual, productIdQual));
                if (count == 0) {
                    AgentApplicationRest agentAppRest = new AgentApplicationRest();
                    agentAppRest.setAgentId(agentId);
                    agentAppRest.setProductId(osApp.getId());
                    try {
                        AgentApplication agentApp =
                                agentAppService.createInNewTransaction(callContext, agentAppRest);
                        serviceLogger.debug("Created Agent app id = {}", agentApp.getId());
                    } catch (Exception e) {
                        serviceLogger.error("Error while creating agent app of swName:{} for agent id:{}",
                                applicationName, agentId, e);
                    }
                } else {
                    serviceLogger.debug(
                            "Agentapplication already exits for agent id = {} and product id = {}", agentId,
                            osApp.getId());
                }
            }
        } else {
            // Rempve Agent-application relation
            if (applicationName == null) {
                applicationName = Constants.STRING_DEFAULT;
            }
            Qualification appQual =
                    QualUtils.buildRelationalQual(FlotoBase_.NAME, RelationalOperator.Contains,
                            applicationName);
            FlotoBaseRepository<OsApplication> osAppRepo =
                    repositoryResolverService.getRelevantRepositoryByManagedModel(
                            FlotoManagedModels.OSAPPLICATION);

            List<Long> osApp = osAppRepo.getAllIdsByQual(callContext, appQual, null);
            if (CollectionUtils.isNotEmpty(osApp)) {
                Qualification agentIdQual =
                        QualUtils.buildRelationalQual(AgentApplication_.AGENT_ID, RelationalOperator.Equal,
                                agentId);
                Qualification productIdQual =
                        QualUtils.buildRelationalQual(AgentApplication_.PRODUCT_ID, RelationalOperator.In,
                                osApp);
                agentAppService.deleteByQualificationInNewTransaction(callContext,
                        QualUtils.andQual(agentIdQual, productIdQual));
                serviceLogger.debug("App ->{} is disbaled for Agent ID:{}, removing  support ",
                        applicationName, agentId);
            }
        }
    }

    private Set<String> extractAllKbs(String sysInfo) {
        Set<String> allMatches = new HashSet<>();
        if (FlotoStringUtils.isNotBlank(sysInfo)) {
            Matcher m3 = PatchConstants.ONLY_HOTFIX_PATTERN.matcher(sysInfo);
            if (m3.find()) {
                String[] matches =
                        PatchConstants.ONLY_HOTFIX_PATTERN.matcher(sysInfo).results().map(MatchResult::group)
                                .toArray(String[]::new);
                if (ArrayUtils.isNotEmpty(matches)) {
                    for (String mm : matches) {
                        allMatches.add(mm.trim().replace("KB", ""));
                    }
                }
            }
        }

        return allMatches;
    }


    @Override
    public void after_deleteByQualification(CallContext callContext, Qualification deleteQual,
            List<Agent> deletedObjectInfo) {
        super.after_deleteByQualification(callContext, deleteQual, deletedObjectInfo);
        if (CollectionUtils.isNotEmpty(deletedObjectInfo)) {
            Set<Long> agentIds = deletedObjectInfo.stream().map(e -> e.getId()).collect(Collectors.toSet());
            agentPatchCrossService.deleteAgentFromEndPointScope(callContext, agentIds);
            agentPatchCrossService.deleteAgentRelations(callContext, agentIds);
            agentPatchCrossService.updateAgentNominationOnAgentDelete(callContext, deletedObjectInfo);
            Qualification refIdQual = QualUtils.refIdQual(agentIds);
            Qualification refModelQual = QualUtils.refModelQual(FlotoManagedModels.AGENT);

            agentLogDownloadHistoryService.deleteByQualification(callContext,
                    QualUtils.andQual(refIdQual, refModelQual));
            for (Agent agent : deletedObjectInfo) {
                createAuditForDelete(callContext, agent);
            }
        }
    }

    private void createAuditForAgentCreation(CallContext callContext, Agent agent) {
        String displayName;
        if (agent.isMobileAgent()) {
            displayName = "Mobile Agent Added";
        } else {
            displayName = "Discovery Agent Added";
        }

        StringBuilder sb = new StringBuilder(150);
        sb.append(agent.getName());
        sb.append(" is Added With");
        sb.append(" IP Address as ").append(agent.getIpAddress());
        sb.append(", Host Name as ").append(agent.getHostName());
        sb.append(", Os Name as ").append(agent.getOsName());
        sb.append(", Version as ").append(agent.getAgentVersion());
        if (callContext.getClient() != null && !FlotoCoreClientKeys.AGENT_KEY.equals(
                callContext.getClient().getClientId()) && callContext.getClient()
                .isCustomConfiguredClient()) {
            sb.append(", Credential Profile Name as ")
                    .append(credentialProfileService.getNameByFlotoClientId(callContext,
                            callContext.getClient().getId()));
        }
        sb.append(".");

        AdminAuditRest rest = new AdminAuditRest();
        rest.setDisplayName(displayName);
        rest.setAuditString(sb.toString());
        rest.setRefModel(FlotoManagedModels.AGENT);
        rest.setRefId(agent.getId());
        rest.setAuditEvent(AuditEvent.ADDED);
        rest.setPerformerId(callContext.getUser().getId());
        rest.setPerformerType(PerformerType.USER);
        abstractAuditService.createAuditItem(callContext, rest, FlotoManagedModels.ADMIN);
    }

    private void createAuditForDelete(CallContext callContext, Agent agent) {
        StringBuilder sb = new StringBuilder(50);
        String displayName = getDisplayName(callContext, agent);
        sb.append(agent.getName());
        if (FlotoUtils.isForceDeleteCall(callContext) || agent.isMobileAgent()) {
            sb.append(" is Permanently Deleted.");
        } else {
            sb.append(" is Archived.");
        }
        AdminAuditRest rest = new AdminAuditRest();
        rest.setDisplayName(displayName);
        rest.setAuditString(sb.toString());
        rest.setRefModel(FlotoManagedModels.AGENT);
        rest.setRefId(agent.getId());
        rest.setAuditEvent(AuditEvent.DELETE);
        rest.setPerformerId(callContext.getUser().getId());
        rest.setPerformerType(PerformerType.USER);
        abstractAuditService.createAuditItem(callContext, rest, FlotoManagedModels.ADMIN);
    }

    @NotNull
    private static String getDisplayName(CallContext callContext, Agent agent) {
        String displayName;
        if (agent.isMobileAgent()) {
            displayName = "Delete Mobile Agent";
        } else if (FlotoUtils.isForceDeleteCall(callContext)) {
            displayName = "Delete Agent";
        } else {
            displayName = "Archive Agent";
        }
        return displayName;
    }

    @Override
    public void saveDiscoveredLinuxPatchesToAgent(CallContext callContext, long agentId,
            ScannedPatchDataRest request) {
        Agent agent = getById(callContext, agentId, false);
        if (agent != null) {
            if (agent.isPatchEnabled()) {
                serviceLogger.debug("Processing the scanned data of agent id :{}", agentId);
                FlotoBaseRepository<LinuxOsApplication> osAppRepo =
                        repositoryResolverService.getRelevantRepositoryByManagedModel(
                                FlotoManagedModels.LINUX_OS_APPLICATION);
                FlotoBaseRepository<AgentApplication> agentAppRepo =
                        repositoryResolverService.getRelevantRepositoryByManagedModel(
                                FlotoManagedModels.AGENT_APPLICATION);

                if (request != null && MapUtils.isNotEmpty(request.getPatchData())) {
                    processCentosInstalledList(callContext, agentId, request, osAppRepo, agentAppRepo, agent);
                    processUbuntuInstalledList(callContext, agentId, request, osAppRepo, agentAppRepo, agent);
                }
                buildLinuxOsApplicationObject(callContext, agent);
            } else {
                serviceLogger.debug("Agent is not found in endpoint scope");
            }
        } else {
            serviceLogger.debug("No agent found with agent id : {}", agentId);
        }

    }

    private void createOsApplicationForOsName(CallContext callContext, Agent agent) {
        FlotoBaseService<OsApplication, OsApplicationRest> osAppService =
                serviceResolverService.getRelevantServiceByManagedModel(FlotoManagedModels.OSAPPLICATION);
        String osName = agent.getOsName();
        if (osName != null) {
            osName = osName.trim();
        }
        OsApplication osApp = osAppService.getByNameIgnoreCase(callContext, osName);
        if (osApp == null) {
            OsApplicationRest osAppRest = new OsApplicationRest();
            osAppRest.setProductType(ProductType.OS);
            osAppRest.setPlatform(agent.getPlatform());
            osAppRest.setName(osName);
            osAppRest.setHidden(false);
            osApp = osAppService.createInNewTransaction(callContext, osAppRest);
            serviceLogger.debug("Created osApp name = {}, id ={}", osApp.getName(), osApp.getId());
        }
    }

    private void buildLinuxOsApplicationObject(CallContext callContext, Agent agent) {
        LinuxOsApplicationRest rest = new LinuxOsApplicationRest();
        rest.setName(agent.getOsName());
        rest.setVersion(agent.getOsVersion());
        rest.setPlatform(agent.getPlatform());
        rest.setProductType(ProductType.OS);
        // TODO: set arch and distro
        LinuxOsApplication losApp = agentPatchCrossService.createOrUpdateLinuxOsApp(callContext, rest);
        if (losApp != null) {
            Qualification agentIdQual =
                    QualUtils.buildRelationalQual(AgentApplication_.AGENT_ID, RelationalOperator.Equal,
                            agent.getId());

            Qualification productIdQual =
                    QualUtils.buildRelationalQual(AgentApplication_.PRODUCT_ID, RelationalOperator.Equal,
                            losApp.getId());
            FlotoBaseRepository<AgentApplication> agentAppRepo =
                    repositoryResolverService.getRelevantRepositoryByManagedModel(
                            FlotoManagedModels.AGENT_APPLICATION);
            // TODO: Memory leak
            List<AgentApplication> existingAgentApps =
                    agentAppRepo.getAllByQual(callContext, QualUtils.andQual(agentIdQual, productIdQual),
                            null);
            if (CollectionUtils.isEmpty(existingAgentApps)) {
                AgentApplication agentApp = new AgentApplication();
                agentApp.setAgentId(agent.getId());
                agentApp.setProductId(losApp.getId());
                agentApp.setOsApplicationType(OsApplicationType.LINUX_OS_APP);
                agentApp = agentAppRepo.save(callContext, agentApp);
                serviceLogger.debug("Created agent application id :{}", agentApp.getId());
            }
        }
    }

    private void processUbuntuInstalledList(CallContext callContext, long agentId,
            ScannedPatchDataRest request, FlotoBaseRepository<LinuxOsApplication> osAppRepo,
            FlotoBaseRepository<AgentApplication> agentAppRepo, Agent agent) {
        OsPlatform platform = agent.getPlatform();

        Object installedPkgList = switch (platform) {
            case UNIX_UBUNTU, UNIX_MINT ->
                    request.getPatchData().get(PatchConstants.CLASS_LINUX_UBUNTU_INSTALLED_PACKAGE_INFO);
            case UNIX_DEBIAN ->
                    request.getPatchData().get(PatchConstants.CLASS_LINUX_DEBIAN_INSTALLED_PACKAGE_INFO);
            case UNIX_PARDUS ->
                    request.getPatchData().get(PatchConstants.CLASS_LINUX_PARDUS_INSTALLED_PACKAGE_INFO);
            default -> null;
        };

        if (installedPkgList instanceof String pkgListLinesAsStr) {
            String[] pkgListLines = FlotoStringUtils.splitByWholeSeparator(pkgListLinesAsStr, "\n");

            Set<Long> osAppIdsSet = new HashSet<>();

            if (pkgListLines != null && pkgListLines.length > 0) {
                List<List<String>> partitionList = Lists.partition(Arrays.asList(pkgListLines), 500);
                for (List<String> chunk : partitionList) {
                    List<LinuxOsApplication> osApplicationList =
                            processUbuntuPackageChunk(callContext, agent, chunk, platform);
                    osAppIdsSet.addAll(createOrUpdateOsAppList(callContext, osAppRepo, osApplicationList));
                }
                createAgentApplication(callContext, agentId, agentAppRepo, osAppIdsSet);
                // removeOldOsAppRelations(callContext, agentId, osAppIdsSet, agentAppRepo);
            }
        }
    }

    private Set<Long> createOrUpdateOsAppList(CallContext callContext,
            FlotoBaseRepository<LinuxOsApplication> osAppRepo, List<LinuxOsApplication> osApplicationList) {
        Set<Long> osAppIdsSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(osApplicationList)) {
            List<LinuxOsApplication> existingOsApp =
                    agentPatchCrossService.getLinuxOsAppListByNameWithVersion(callContext,
                            osApplicationList.parallelStream().map(LinuxOsApplication::getNameWithVersion)
                                    .collect(Collectors.toList()));

            if (CollectionUtils.isNotEmpty(existingOsApp)) {
                Set<String> existingAppNamesWithVersionList =
                        existingOsApp.parallelStream().map(LinuxOsApplication::getNameWithVersion)
                                .collect(Collectors.toSet());
                osAppIdsSet.addAll(existingOsApp.parallelStream().map(LinuxOsApplication::getId)
                        .collect(Collectors.toSet()));
                osApplicationList = osApplicationList.parallelStream()
                        .filter(e -> !existingAppNamesWithVersionList.contains(e.getNameWithVersion()))
                        .collect(Collectors.toList());
            }
            osApplicationList = osAppRepo.saveAll(osApplicationList);
            List<Long> osAppIds = osApplicationList.parallelStream().map(FlotoEntity::getId).toList();
            osAppIdsSet.addAll(osAppIds);
        }
        return osAppIdsSet;
    }

    private List<LinuxOsApplication> processUbuntuPackageChunk(CallContext callContext, Agent agent,
            List<String> chunk, OsPlatform platform) {
        List<LinuxOsApplication> osAppList = new ArrayList<>();
        for (String pkgLine : chunk) {
            pkgLine = ParseUtil.getSplitVal(pkgLine, "\\[", 0).trim();
            String[] packageInfo = pkgLine.split(" ");
            String pkgName = null;
            String version = null;
            String arch = null;
            String distro = null;
            String pkgBaseInfo = packageInfo[0];
            pkgName = ParseUtil.getSplitVal(pkgBaseInfo, "/", 0);
            distro = ParseUtil.getSplitVal(pkgBaseInfo, "/", 1);
            distro = distro.split(",")[distro.split(",").length - 1];
            if (packageInfo.length == 3) {
                version = FlotoStringUtils.trim(packageInfo[1]);
                arch = FlotoStringUtils.trim(packageInfo[2]);
            }

            if (distro.startsWith("now")) {
                String[] distroInfoSplit = FlotoStringUtils.split(distro, " ");
                if (distroInfoSplit.length >= 2) {
                    version = FlotoStringUtils.trim(distroInfoSplit[1]);
                    arch = FlotoStringUtils.trim(distroInfoSplit[2]);
                }
                distro = PatchUtils.getLinuxDistributionNameByOsName(agent.getOsName());
            } else if (distro.contains("unknown")) {
                distro = PatchUtils.getLinuxDistributionNameByOsName(agent.getOsName());
            }

            if (FlotoStringUtils.isNotBlank(pkgName) && FlotoStringUtils.isNotBlank(version)) {
                LinuxOsApplication osApp =
                        buildLinuxOsApplicationObject(callContext, pkgName, version, arch, distro, platform);
                osAppList.add(osApp);
                serviceLogger.debug("Adding linux os app name : {}, version : {}", osApp.getName(),
                        osApp.getVersion());
            }
        }
        return osAppList;
    }

    private void processCentosInstalledList(CallContext callContext, long agentId,
            ScannedPatchDataRest request, FlotoBaseRepository<LinuxOsApplication> osAppRepo,
            FlotoBaseRepository<AgentApplication> agentAppRepo, Agent agent) {
        OsPlatform platform = agent.getPlatform();
        Object installedPkgList = switch (platform) {
            case UNIX_CENTOS ->
                    request.getPatchData().get(PatchConstants.CLASS_LINUX_CENTOS_INSTALLED_PACKAGE_INFO);
            case UNIX_RED_HAT ->
                    request.getPatchData().get(PatchConstants.CLASS_LINUX_REDHAT_INSTALLED_PACKAGE_INFO);
            case UNIX_OPEN_SUSE, UNIX_SUSE ->
                    request.getPatchData().get(PatchConstants.CLASS_LINUX_OPENSUSE_INSTALLED_PACKAGE_INFO);
            case UNIX_ORACLE_LINUX -> request.getPatchData()
                    .get(PatchConstants.CLASS_LINUX_ORACLE_LINUX_INSTALLED_PACKAGE_INFO);
            case UNIX_ALMA_LINUX ->
                    request.getPatchData().get(PatchConstants.CLASS_LINUX_ALMA_LINUX_INSTALLED_PACKAGE_INFO);
            case UNIX_ROCKY_LINUX ->
                    request.getPatchData().get(PatchConstants.CLASS_LINUX_ROCKY_LINUX_INSTALLED_PACKAGE_INFO);
            default -> null;
        };
        if (installedPkgList instanceof String pkgListLinesAsStr) {
            String[] pkgListLines = FlotoStringUtils.splitByWholeSeparator(pkgListLinesAsStr,
                    PatchConstants.LINUX_PKGINFO_SEPARATOR);
            if (ArrayUtils.isNotEmpty(pkgListLines)) {
                Set<Long> osAppIdsSet = new HashSet<>();
                List<String> pkgListLinesList = Arrays.asList(pkgListLines);
                List<List<String>> partitionList = Lists.partition(pkgListLinesList, 500);
                for (List<String> pkgList : partitionList) {
                    List<LinuxOsApplication> osApplicationList =
                            processAllPackageLines(callContext, pkgList, agent, platform);
                    osAppIdsSet.addAll(createOrUpdateOsAppList(callContext, osAppRepo, osApplicationList));
                }
                createAgentApplication(callContext, agentId, agentAppRepo, osAppIdsSet);
                // removeOldOsAppRelations(callContext, agentId, osAppIdsSet, agentAppRepo);
            }
        }
    }

    private List<LinuxOsApplication> processAllPackageLines(CallContext callContext, List<String> pkgList,
            Agent agent, OsPlatform platform) {
        List<LinuxOsApplication> osApplicationList = null;
        if (CollectionUtils.isNotEmpty(pkgList)) {
            osApplicationList = new ArrayList<>();
            for (String pkgLine : pkgList) {
                String[] pkgInfoArr = FlotoStringUtils.splitByWholeSeparator(pkgLine,
                        PatchConstants.LINUX_PKGFIELDS_SEPARATOR);
                if (pkgInfoArr.length > 0) {
                    String pkgName = FlotoStringUtils.trim(pkgInfoArr[0]);
                    String version = FlotoStringUtils.trim(pkgInfoArr[1]);
                    String arch = null;
                    if (pkgInfoArr.length >= 3) {
                        arch = FlotoStringUtils.trim(pkgInfoArr[2]);
                    }
                    LinuxOsApplication osApp =
                            buildLinuxOsApplicationObject(callContext, pkgName, version, arch,
                                    agentPatchCrossService.getDistroByOsName(agent.getOsName()), platform);
                    osApplicationList.add(osApp);
                    serviceLogger.debug("Adding linux os app name : {}, version : {}", osApp.getName(),
                            osApp.getVersion());
                }
            }
        }
        return osApplicationList;
    }

    private LinuxOsApplication buildLinuxOsApplicationObject(CallContext callContext, String pkgName,
            String version, String arch, String distro, OsPlatform platform) {
        LinuxOsApplication osApp = new LinuxOsApplication();
        osApp.setName(pkgName);
        osApp.setVersion(version);
        osApp.setArch(arch);
        osApp.setCreatedTime(System.currentTimeMillis());
        osApp.setCreatedById(callContext.getUser().getId());
        osApp.setDistribution(distro);
        osApp.setNameWithVersion(StringUtils.join(osApp.getName(), PatchConstants.PKG_NAME_VERSION_SEPARATOR,
                osApp.getVersion()));
        osApp.setNameWithDistro(StringUtils.join(osApp.getName(), PatchConstants.PKG_NAME_DISTRO_SEPARATOR,
                osApp.getDistribution()));
        osApp.setPlatform(platform);
        osApp.setProductType(ProductType.APPLICATION);
        return osApp;

    }

    private void createAgentApplication(CallContext callContext, long agentId,
            FlotoBaseRepository<AgentApplication> agentAppRepo, Set<Long> osAppIdsSet) {
        Set<Long> osAppIdToBeDelete = new HashSet<>();
        serviceLogger.debug("Checking agent app relation of os app id : {}", osAppIdsSet);
        Qualification agentIdQual =
                QualUtils.buildRelationalQual(AgentApplication_.AGENT_ID, RelationalOperator.Equal, agentId);

        Qualification productIdQual =
                QualUtils.buildRelationalQual(AgentApplication_.PRODUCT_ID, RelationalOperator.In,
                        osAppIdsSet);

        List<Long> existingProductIdsApps =
                agentAppRepo.getAllPartByQual(callContext, QualUtils.andQual(agentIdQual, productIdQual),
                        null, Long.class, Lists.newArrayList(AgentApplication_.PRODUCT_ID));
        if (CollectionUtils.isNotEmpty(existingProductIdsApps)) {
            Set<Long> existingProductIdsRelation = Sets.newHashSet(existingProductIdsApps);
            osAppIdToBeDelete = SetUtils.difference(existingProductIdsRelation, Sets.newHashSet(osAppIdsSet));
            osAppIdsSet.removeAll(existingProductIdsRelation);
        }
        if (CollectionUtils.isNotEmpty(osAppIdsSet)) {
            List<AgentApplication> agentApps = new ArrayList<>();
            for (Long osAppId : osAppIdsSet) {
                AgentApplication agentApp = new AgentApplication();
                agentApp.setAgentId(agentId);
                agentApp.setProductId(osAppId);
                agentApp.setOsApplicationType(OsApplicationType.LINUX_OS_APP);
                agentApps.add(agentApp);
            }
            serviceLogger.debug("Total AgentApplication builded :{}", agentApps.size());
            if (CollectionUtils.isNotEmpty(agentApps)) {
                agentAppRepo.saveAll(agentApps);
            }
        } else {
            serviceLogger.debug("Agent application already exists");
        }
        if (CollectionUtils.isNotEmpty(osAppIdToBeDelete)) {
            Qualification productIdNotInQual =
                    QualUtils.buildRelationalQual(AgentApplication_.PRODUCT_ID, RelationalOperator.In,
                            osAppIdToBeDelete);
            agentAppRepo.deleteAllByQual(callContext, QualUtils.andQual(agentIdQual, productIdNotInQual));
        }
    }

    @Override
    public String getRepoFileByAgent(CallContext callContext, long agentId, String pollerUrl) {
        String fileContent = null;
        Agent agent = getById(callContext, agentId, false);
        String repofilePath = "";

        if (agent != null) {
            switch (agent.getPlatform()) {
                case UNIX_OPEN_SUSE, UNIX_SUSE ->
                        repofilePath = Constants.QUERIES_REPOS_OPENSUSE_REPO_OPENSUSE_REPO;
                case UNIX_CENTOS -> {
                    if (FlotoStringUtils.startsWith(agent.getOsVersion(), "10")
                            && FlotoStringUtils.containsIgnoreCase(agent.getOsName(), "Stream")) {
                        repofilePath = Constants.QUERIES_REPOS_CENTOS_REPO_CENTOS_10_STREAM_REPO;
                    } else if (FlotoStringUtils.startsWith(agent.getOsVersion(), "9")
                            && FlotoStringUtils.containsIgnoreCase(agent.getOsName(), "Stream")) {
                        repofilePath = Constants.QUERIES_REPOS_CENTOS_REPO_CENTOS_9_STREAM_REPO;
                    }
                }

                case UNIX_ORACLE_LINUX -> {
                    if (FlotoStringUtils.startsWith(agent.getOsVersion(), "7")) {
                        repofilePath = Constants.QUERIES_REPOS_ORACLE_LINUX_7_REPO;
                    } else if (FlotoStringUtils.startsWith(agent.getOsVersion(), "8")) {
                        repofilePath = Constants.QUERIES_REPOS_ORACLE_LINUX_8_REPO;
                    } else if (FlotoStringUtils.startsWith(agent.getOsVersion(), "9")) {
                        repofilePath = Constants.QUERIES_REPOS_ORACLE_LINUX_9_REPO;
                    }
                }
                case UNIX_ALMA_LINUX -> repofilePath = Constants.QUERIES_REPOS_ALMA_LINUX_REPO;
                case UNIX_ROCKY_LINUX -> repofilePath = Constants.QUERIES_REPOS_ROCKY_LINUX_REPO;
                default -> {
                    //do nothing
                }
            }

            try {
                if (OsPlatform.UNIX_RED_HAT.equals(agent.getPlatform())) {
                    AgentSetting agentSetting = agentSettingService.get(callContext);
                    if (agentSetting.isPerformRedHatPatchManagementThroughSatelliteServer()) {
                        fileContent = getRepofileForRedHatSatellite(callContext, agent);
                    } else {
                        repofilePath = getRepofilePathForAgentNomination(agent);

                        if (FlotoStringUtils.isNotBlank(repofilePath)) {
                            fileContent = getFileContent(repofilePath);
                        }
                    }
                } else {
                    if (FlotoStringUtils.isNotBlank(repofilePath)) {
                        fileContent = getFileContent(repofilePath);
                    }
                }
            } catch (IOException e) {
                serviceLogger.error("error while reading repo file  {}", repofilePath, e);
            }

            if (FlotoStringUtils.isNotBlank(fileContent)) {
                if (FlotoStringUtils.isNotBlank(pollerUrl)) {

                    String decodedPolerUrl =
                            new String(Base64.getDecoder().decode(pollerUrl), StandardCharsets.UTF_8);
                    String urlPreFix = decodedPolerUrl + "/patchmirror/" + callContext.getTenantIdentifier()
                            + "/patch_repo";
                    fileContent = fileContent.replace("${downloadurl}", urlPreFix);
                } else {
                    AdminValue value =
                            tenantPreferenceService.getValue(callContext, TenantPreference.BASE_URL);
                    String downloadUrl = value.getStringValue();
                    String urlPreFix =
                            downloadUrl + "/patchmirror/" + callContext.getTenantIdentifier() + "/patch_repo";
                    fileContent = fileContent.replace("${downloadurl}", urlPreFix);
                }
            }
        }

        return fileContent;
    }

    private String getFileContent(String repofilePath) throws IOException {
        StringBuilder resultStringBuilder = new StringBuilder(800);
        try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(repofilePath);
                BufferedReader br = new BufferedReader(new InputStreamReader(is))) {
            String line;
            while ((line = br.readLine()) != null) {
                resultStringBuilder.append(line).append("\n");
            }
        }
        return resultStringBuilder.toString();
    }

    private String getRepofileForRedHatSatellite(CallContext callContext, Agent agent) {
        String fileContent = "";
        List<RedHatSatelliteRepo> redHatSatelliteRepoList =
                redHatSatelliteRepoService.searchByQualification(callContext, null, null);
        List<String> repoFileData = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(redHatSatelliteRepoList)) {
            for (RedHatSatelliteRepo redHatAgentSatelliteRepo : redHatSatelliteRepoList) {
                RedHatSatelliteRepo applicableRedHatAgentSatelliteRepo = null;
                serviceLogger.info("Checking repo with name {} for agent {}",
                        redHatAgentSatelliteRepo.getName(), agent.getName());
                if (agent.getOsVersionNumber().startsWith("7")) {
                    if (OsVariant.SERVER.equals(agent.getOsVariant())
                            && redHatAgentSatelliteRepo.getMinorVersion().contains("7Server")) {
                        String content = buildRepoContent(redHatAgentSatelliteRepo);
                        if (FlotoStringUtils.isNotBlank(content)) {
                            repoFileData.add(content);
                        }
                    } else if (redHatAgentSatelliteRepo.getMinorVersion().contains("7Workstation")) {
                        String content = buildRepoContent(redHatAgentSatelliteRepo);
                        if (FlotoStringUtils.isNotBlank(content)) {
                            repoFileData.add(content);
                        }
                    }
                } else {
                    if (OsVariant.SERVER.equals(agent.getOsVariant())) {
                        if (redHatAgentSatelliteRepo.getMajorVersion()
                                .equals(redHatAgentSatelliteRepo.getMinorVersion())
                                && agent.getOsVersionNumber()
                                .startsWith(redHatAgentSatelliteRepo.getMajorVersion())) {
                            applicableRedHatAgentSatelliteRepo = redHatAgentSatelliteRepo;
                        } else if (!redHatAgentSatelliteRepo.getMajorVersion()
                                .equals(redHatAgentSatelliteRepo.getMinorVersion())
                                && agent.getOsVersionNumber()
                                .equals(redHatAgentSatelliteRepo.getMinorVersion())) {
                            applicableRedHatAgentSatelliteRepo = redHatAgentSatelliteRepo;
                        }
                        if (applicableRedHatAgentSatelliteRepo != null) {
                            String content = buildRepoContent(applicableRedHatAgentSatelliteRepo);
                            if (FlotoStringUtils.isNotBlank(content)) {
                                repoFileData.add(content);
                            }
                        }
                    } else if (OsVariant.WORKSTATION.equals(agent.getOsVariant())
                            && redHatAgentSatelliteRepo.getFullPath().toLowerCase().contains("workstation")
                            && agent.getOsVersionNumber()
                            .startsWith(redHatAgentSatelliteRepo.getMajorVersion())) {
                        String content = buildRepoContent(redHatAgentSatelliteRepo);
                        if (FlotoStringUtils.isNotBlank(content)) {
                            repoFileData.add(content);
                        }
                    }

                }

            }
        }

        if (CollectionUtils.isNotEmpty(repoFileData)) {
            fileContent = String.join("\n\n", repoFileData);
        }

        return fileContent;
    }

    private String buildRepoContent(RedHatSatelliteRepo redHatAgentSatelliteRepo) {
        String content = null;
        try {
            content = getFileContent("queries/repos/redhat-repo/redhatsatelliterepoformate.repo");

            String label = redHatAgentSatelliteRepo.getName().replaceAll(" ", "-").replaceAll("\\.", "_");
            label += "-mtdt";

            String baseUrl = "${downloadurl}/" + OsPlatform.UNIX_RED_HAT.getOsFileDirName()
                    + redHatAgentSatelliteRepo.getDirPath();

            content = content.replace("${name}", redHatAgentSatelliteRepo.getName());
            content = content.replace("${lable}", label);
            content = content.replace("${baseurl}", baseUrl);
            serviceLogger.debug("processed repo with content {}", content);
        } catch (IOException e) {
            serviceLogger.error("error while reading redhat satellite repo file ", e);
        }
        return content;
    }

    private String getRepofilePathForAgentNomination(Agent agent) {
        String repofilePath = "";
        if (agent.getOsVariant() == OsVariant.SERVER) {
            if (FlotoStringUtils.startsWith(agent.getOsVersion(), "8")) {
                repofilePath = Constants.QUERIES_REPOS_REDHAT_REPO_SERVER_REDHAT_8_REPO;
            } else if (FlotoStringUtils.startsWith(agent.getOsVersion(), "7")) {
                repofilePath = Constants.QUERIES_REPOS_REDHAT_REPO_SERVER_REDHAT_7_REPO;
            } else if (FlotoStringUtils.startsWith(agent.getOsVersion(), "9")) {
                repofilePath = Constants.QUERIES_REPOS_REDHAT_REPO_SERVER_REDHAT_9_REPO;
            } else if (FlotoStringUtils.startsWith(agent.getOsVersion(), "10")) {
                repofilePath = Constants.QUERIES_REPOS_REDHAT_REPO_SERVER_REDHAT_10_REPO;
            }
        } else if (agent.getOsVariant() == OsVariant.WORKSTATION) {
            if (FlotoStringUtils.startsWith(agent.getOsVersion(), "8")) {
                repofilePath = Constants.QUERIES_REPOS_REDHAT_REPO_WORKSTATION_REDHAT_8_REPO;
            } else if (FlotoStringUtils.startsWith(agent.getOsVersion(), "7")) {
                repofilePath = Constants.QUERIES_REPOS_REDHAT_REPO_WORKSTATION_REDHAT_7_REPO;
            } else if (FlotoStringUtils.startsWith(agent.getOsVersion(), "9")) {
                repofilePath = Constants.QUERIES_REPOS_REDHAT_REPO_WORKSTATION_REDHAT_9_REPO;
            } else if (FlotoStringUtils.startsWith(agent.getOsVersion(), "10")) {
                repofilePath = Constants.QUERIES_REPOS_REDHAT_REPO_WORKSTATION_REDHAT_10_REPO;
            }
        }
        return repofilePath;
    }

    @Override
    public String getDistFileByAgent(CallContext callContext, long agentId, String osVersion) {

        if (FlotoStringUtils.isBlank(osVersion)) {
            throw new FlotoException(FlotoError.PATCH_OSVERSION_IS_NULL);
        }

        String distfilePath = "";
        File file = fileStorageService.getPatchDbByCategoryName(callContext, FlotoFileType.PATCH_REPO,
                PatchConstants.MACOS.toLowerCase());
        if (file.exists() && file.isDirectory()) {
            if (osVersion.startsWith(PatchConstants.MACOS_VERSION_10)) {
                distfilePath = file.getAbsolutePath() + File.separator + PatchConstants.MACOS_VERSION_10_15
                        + PatchConstants.ZIP_FILE_EXTENSION_7_Z;
            } else if (osVersion.startsWith(PatchConstants.MACOS_VERSION_11)) {
                distfilePath = file.getAbsolutePath() + File.separator + PatchConstants.MACOS_VERSION_10_16
                        + PatchConstants.ZIP_FILE_EXTENSION_7_Z;
            } else if (osVersion.startsWith(PatchConstants.MACOS_VERSION_12)) {
                distfilePath = file.getAbsolutePath() + File.separator + PatchConstants.MACOS_VERSION_12
                        + PatchConstants.ZIP_FILE_EXTENSION_7_Z;
            } else if (osVersion.startsWith(PatchConstants.MACOS_VERSION_13)) {
                distfilePath = file.getAbsolutePath() + File.separator + PatchConstants.MACOS_VERSION_13
                        + PatchConstants.ZIP_FILE_EXTENSION_7_Z;
            } else if (osVersion.startsWith(PatchConstants.MACOS_VERSION_14)) {
                distfilePath = file.getAbsolutePath() + File.separator + PatchConstants.MACOS_VERSION_14
                        + PatchConstants.ZIP_FILE_EXTENSION_7_Z;
            } else if (osVersion.startsWith(PatchConstants.MACOS_VERSION_15)) {
                distfilePath = file.getAbsolutePath() + File.separator + PatchConstants.MACOS_VERSION_15
                        + PatchConstants.ZIP_FILE_EXTENSION_7_Z;
            }

        }
        return distfilePath;
    }

    @Override
    public boolean processAgentRefreshInfoData(CallContext callContext, long agentId,
            AgentRefreshInfoRest request) {
        Agent agent = getById(callContext, agentId, false);
        if (agent != null) {
            OsPlatform platform = agent.getPlatform();
            Object subscriptionCommandResult = null;
            Object hostDetails = null;
            if (platform == OsPlatform.UNIX_RED_HAT && request.getPatchData() != null) {
                subscriptionCommandResult =
                        request.getPatchData().get(PatchConstants.CLASS_LINUX_REDHAT_SUBSCRIPTION_DETAILS);
                hostDetails = request.getPatchData().get(PatchConstants.CLASS_LINUX_REDHAT_HOST_DETAILS);
            }
            String sku = "";
            AgentRest rest = new AgentRest();
            if (subscriptionCommandResult instanceof String subscriptionInfo && FlotoStringUtils.isNotBlank(
                    subscriptionCommandResult.toString())) {
                String[] propertyList = subscriptionInfo.split("\n");
                if (ArrayUtils.isNotEmpty(propertyList)) {
                    for (String prop : propertyList) {
                        String fieldName = ParseUtil.getSplitVal(prop, ":", 0).trim().toLowerCase();
                        String fieldValue = ParseUtil.getSplitVal(prop, ":", 1).trim();
                        if (FlotoStringUtils.equals("sku", fieldName)) {
                            sku = fieldValue.trim();
                        }
                    }
                }
            }
            rest.setOsVariant(PatchUtils.getOsvariant(sku, hostDetails));
            rest.addInPatchMap("osVariant", rest.getOsVariant());
            updatePartial(callContext, agentId, rest);
            serviceLogger.debug("AgentId : Updated successfully with variant");
        }
        return true;
    }

    @Override
    public File downloadGoRuntimeByOsPlatform(OsPlatform platform, OsArchitecture osArch) {
        String configDir = environment.getProperty("queryDir");
        String fileName = OsPlatform.WINDOWS == platform ?
                Constants.GO_RUNTIME_FILE_NAME :
                Constants.UNIX_GO_RUNTIME_FILE_NAME;
        return new File(configDir + File.separator + Constants.CMDB_BUILD_FOLDER_NAME + File.separator
                + platform.toValue() + File.separator + osArch.toValue() + File.separator + fileName);
    }

    @Override
    public File getPatternExecutorFileByOsPlatform(OsPlatform platform, OsArchitecture arch) {
        String configDir = environment.getProperty("queryDir");
        String patternExecutorFile = Constants.UNIX_PATTERN_EXECUTOR_FILE_NAME;
        if (platform == OsPlatform.WINDOWS) {
            patternExecutorFile = Constants.WINDOWS_PATTERN_EXECUTOR_FILE_NAME;
        }
        return new File(configDir + File.separator + Constants.CMDB_BUILD_FOLDER_NAME + File.separator
                + platform.toValue() + File.separator + arch.toValue() + File.separator
                + patternExecutorFile);
    }

    @Override
    public String getCheckSumForPatternExecutorByOsPlatform(OsPlatform platform, OsArchitecture arch) {
        serviceLogger.debug(
                "Process started to get CheckSum For PatternExecutor By OsPlatform : {} Arch : {}", platform,
                arch);
        String hashcode = "";
        try {
            File file = getPatternExecutorFileByOsPlatform(platform, arch);
            if (file.exists()) {
                BasicFileAttributes attr = Files.readAttributes(file.toPath(), BasicFileAttributes.class);
                FileTime fileCreationTime = attr.creationTime();

                if (MapUtils.isNotEmpty(patternExecutorHashCodeMap) && patternExecutorHashCodeMap.containsKey(
                        platform)) {
                    Map<Long, String> hashcodeMap = patternExecutorHashCodeMap.get(platform);
                    if (MapUtils.isNotEmpty(hashcodeMap) && hashcodeMap.containsKey(
                            fileCreationTime.toMillis())) {
                        hashcode = hashcodeMap.get(fileCreationTime.toMillis());
                    }
                }

                if (FlotoStringUtils.isBlank(hashcode)) {
                    serviceLogger.debug(
                            "PatternExecutor is updated so generating new hashcode for os : {}, file creation time : {}",
                            platform, fileCreationTime.toMillis());
                    hashcode = SystemProcessUtils.generateSHA256HashCodeForFile(file);
                    patternExecutorHashCodeMap.put(platform, Map.of(fileCreationTime.toMillis(), hashcode));
                }
            } else {
                serviceLogger.debug("PatternExecutor File does not exist for os : {}, arch : {}", platform,
                        arch);
            }
        } catch (Exception e) {
            serviceLogger.error("Error while generating hashcode for pattern executor for os {}, arch : {} :",
                    platform, arch, e);
        }
        serviceLogger.debug(
                "Process completed to get CheckSum For PatternExecutor By OsPlatform : {}, arch : {} , hashcode : {}",
                platform, arch, hashcode);
        return hashcode;
    }

    @Override
    public void associatAgentWithAsset(CallContext callContext, HardwareAsset hardwareAsset, long agentId,
            Agent agent) {
        if (agent != null && hardwareAsset != null) {
            discoveryLogger.debug("Agent={} has differnt Asset={} associated.", agentId, agent.getAssetId());

            if (agent.getAssetId() == 0) {
                discoveryLogger.debug("Associated AssetID is 0.");
            }

            agent.setAssetId(hardwareAsset.getId());
            forceSave(callContext, agent);
            discoveryLogger.debug("Agent Id={} has been updated with Asset Id : {} ", agent.getId(),
                    hardwareAsset.getId());

            discoveryLogger.debug("Unlinking this asset with any other agent");
            Qualification agentIdQual = QualUtils.idNotEqualQual(agentId);

            Qualification assetIdQual =
                    QualUtils.buildRelationalQual(Agent_.ASSET_ID, RelationalOperator.Equal,
                            hardwareAsset.getId());

            List<Agent> agentList =
                    searchByQualification(callContext, QualUtils.andQual(agentIdQual, assetIdQual), null);

            if (CollectionUtils.isNotEmpty(agentList)) {
                for (Agent oldAgent : agentList) {
                    oldAgent.setAssetId(0);
                    discoveryLogger.debug("Removed asset id from agent ID {} : {}", oldAgent.getId(),
                            oldAgent.getName());
                }
            }

            forceSaveAll(callContext, agentList);
        }
    }

    @Override
    public Map<String, Object> prepareAgentDiscoveryContext(CallContext callContext, long id) {
        serviceLogger.debug("precess started to prepare agent discovery context for agent id : {}", id);
        Map<String, Object> agentDiscoveryContext = new HashMap<>();
        Agent agent = getById(callContext, id, false);
        if (agent != null) {
            agentDiscoveryContext.put(GoDiscoveryConstants.CONTEXT_DISCOVERY_TYPE,
                    OsPlatform.WINDOWS == agent.getPlatform() ?
                            DiscoveryDeviceProtocol.WMI.toValue() :
                            DiscoveryDeviceProtocol.SSH.toValue());
            agentDiscoveryContext.put(GoDiscoveryConstants.CONTEXT_IP, agent.getIpAddress());
            agentDiscoveryContext.put("isAgent", true);
            agentDiscoveryContext.put("assetId", agent.getAssetId());

            TenantPreference tenantPreference = tenantPreferenceService.get(callContext);
            if (FlotoValueUtils.getBooleanValue(
                    tenantPreference.getValue(TenantPreference.CI_FETCH_RUNNING_PROCESSES))) {
                agentDiscoveryContext.put(GoDiscoveryConstants.CONTEXT_FETCH_PROCESS_CI_TYPE_LIST,
                        ciTypeService.getCiUniqueNameForProcessDiscovery(callContext));
                AdminValue processNames =
                        tenantPreference.getValue(TenantPreference.FETCH_RUNNING_PROCESS_NAME_LIST);
                if (Objects.nonNull(processNames)) {
                    agentDiscoveryContext.put(GoDiscoveryConstants.CONTEXT_FETCH_PROCESS_NAME_LIST,
                            processNames.getListStringValue());
                }
            }
            if (OsPlatform.WINDOWS != agent.getPlatform() && OsPlatform.MAC != agent.getPlatform()) {
                agentDiscoveryContext.put(GoDiscoveryConstants.CONTEXT_SCAN_LINUX_SOFTWARE_DATA,
                        tenantPreferenceService.getBooleanValueByKey(callContext,
                                TenantPreference.ASSET_FETCH_SOFTWARES_DISCOVERY));
            }

            if (OsPlatform.WINDOWS == agent.getPlatform()) {
                agentDiscoveryContext.put(GoDiscoveryConstants.CONTEXT_ENABLE_VM_DISCOVERY_ON_HYPER_V,
                        tenantPreferenceService.getBooleanValueByKey(callContext,
                                TenantPreference.ENABLE_VM_DISCOVERY_ON_HYPER_V));

                agentDiscoveryContext.put(GoDiscoveryConstants.CONTEXT_SHOULD_FETCH_SHARED_FOLDER,
                        tenantPreferenceService.getBooleanValueByKey(callContext,
                                TenantPreference.FATCH_SHARED_FOLDER_DETAILS));

                agentDiscoveryContext.put(GoDiscoveryConstants.CONTEXT_SHOULD_FETCH_USB_PORT_DETAILS,
                        tenantPreferenceService.getBooleanValueByKey(callContext,
                                TenantPreference.FETCH_USB_PORT_DETAILS));
            }
        }
        serviceLogger.debug("precess completed to prepare agent discovery context for agent id : {}", id);
        return agentDiscoveryContext;
    }

    @Override
    public Map<String, Object> getGoExecutorDetails(CallContext callContext, OsPlatform osPlatform,
            OsArchitecture arch) {
        String hashcode = getCheckSumForPatternExecutorByOsPlatform(osPlatform, arch);
        serviceLogger.info("GO Executor Checksum:{}", hashcode);

        Map<String, Object> versionDetailMap = new HashMap<>();
        versionDetailMap.put(Constants.GO_EXECUTOR_CHECKSUM, hashcode);
        versionDetailMap.put(Constants.PLATFORM, osPlatform);
        versionDetailMap.put(Constants.ARCH, arch);
        versionDetailMap.put(Constants.GO_RUNTIME_VERSION, goRuntimeVersion);
        serviceLogger.debug("Go Runtime Version :{}", goRuntimeVersion);
        return versionDetailMap;
    }

    @Override
    public void creatAgentLogDownloadCommand(CallContext callContext, long agentId) {
        uploadDownloadCommandCreator.createAgentCommand(callContext, agentId, RCWorkType.UPLOAD_LOG, 0);
        agentLogDownloadHistoryService.createHistory(callContext, FlotoManagedModels.AGENT, agentId);
    }

    @Override
    public FlotoObjectPage<Agent> getAgentForInstalledSoftware(CallContext callContext, long assetId,
            Qualification qual, FlotoPageRequest flotoPageRequest, boolean isGetAllAgents) {

        Qualification subQual =
                QualUtils.buildRelationalQual(SoftwareComponent_.SOFTWARE_ASSET_ID, RelationalOperator.Equal,
                        assetId);

        Qualification flotoQueryQualSoftwareCOmponent =
                QualUtils.buildSubQueryQual(FlotoModelPart_.REF_ID, subQual, Agent_.ASSET_ID,
                        FlotoManagedModels.SOFTWARE_COMPONENT);

        Qualification flotoQueryQualOsComponent =
                QualUtils.buildSubQueryQual(FlotoModelPart_.REF_ID, subQual, Agent_.ASSET_ID,
                        FlotoManagedModels.OS_COMPONENT);
        if (!isGetAllAgents) {
            qual = QualUtils.andQual(
                    QualUtils.orQual(flotoQueryQualSoftwareCOmponent, flotoQueryQualOsComponent), qual);
        }

        return selfProxy.searchPageByQualification(callContext, qual, flotoPageRequest);
    }

    @Override
    public Map<String, Object> getServiceDiscoveryCommandExecutionInfo(CallContext callContext, long id) {
        serviceLogger.debug("[getServiceDiscoveryCommandExecutionInfo] process started for agent id {}", id);
        Map<String, Object> serviceCommandTargetDetails = new HashMap<>();
        boolean shouldSkipExecution = false;
        Agent agent = getById(callContext, id, false);
        if (agent != null && agent.getAssetId() != 0) {
            FlotoBaseService<HardwareAsset, HardwareAssetRest> assetService =
                    serviceResolverService.getRelevantServiceByManagedModel(
                            FlotoManagedModels.ASSET_HARDWARE);
            HardwareAsset hwAsset = assetService.getById(callContext, agent.getAssetId(), false);
            if (hwAsset != null) {
                if (hwAsset.isCi() && !hwAsset.isCiInReview() && !hwAsset.isRemoved()) {
                    Qualification workTypeQual =
                            QualUtils.buildRelationalQual(Command_.WORK_TYPE, RelationalOperator.Equal,
                                    RCWorkType.SERVICE_DISCOVERY);
                    Qualification agentIdQual =
                            QualUtils.buildRelationalQual(Command_.AGENT_ID, RelationalOperator.Equal, id);
                    Qualification cmdStateQual =
                            QualUtils.buildRelationalQual(Command_.STATE, RelationalOperator.Equal,
                                    CommandState.YET_TO_RECEIVE);
                    List<Command> commandList = commandService.searchByQualification(callContext,
                            QualUtils.andQual(workTypeQual, agentIdQual, cmdStateQual),
                            new FlotoPageRequest(0, 1, Sort.Direction.DESC, FlotoBase_.CREATED_TIME));
                    if (CollectionUtils.isNotEmpty(commandList)) {
                        serviceCommandTargetDetails.put("commandId", commandList.get(0).getId());
                        shouldSkipExecution = true;
                    }
                } else {
                    shouldSkipExecution = true;
                }
            }
        } else {
            shouldSkipExecution = true;
        }
        serviceCommandTargetDetails.put("shouldSkipExecution", shouldSkipExecution);
        serviceLogger.debug("[getServiceDiscoveryCommandExecutionInfo]  serviceCommandTargetDetails : {}",
                serviceCommandTargetDetails);
        return serviceCommandTargetDetails;
    }

    @Override
    protected String getMangedModelPrefixKey(Agent domainObj) {
        return FlotoManagedModels.AGENT.getName();
    }
}

