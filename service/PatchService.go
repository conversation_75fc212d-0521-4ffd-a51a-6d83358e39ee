package service

import (
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"errors"
	"fmt"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"

	"golang.org/x/mod/semver"
)

type PatchService struct {
	Repository *repository.PatchRepository
}

func NewPatchService() *PatchService {
	return &PatchService{
		Repository: repository.NewPatchRepository(),
	}
}

func (service PatchService) convertToModel(patchRest rest.PatchRest) (*model.Patch, error) {
	var osType common.OsType
	var osArch common.OsArchitecture
	var downloadStatus model.PatchDownloadStatus
	var approvalStatus model.PatchApprovalStatus
	var testStatus model.PatchTestStatus
	var patchSeverity model.PatchSeverity
	var patchUpdateCategory model.PatchUpdateCategory
	var rebootBehaviour model.RebootBehaviour
	var source model.PatchSource
	var status model.PatchStatus

	if patchRest.OsPlatform != "" {
		osType = osType.ToOsType(patchRest.OsPlatform)
	}
	if patchRest.OsArch != "" {
		osArch = osArch.ToOsArch(patchRest.OsArch)
	}
	downloadStatus = downloadStatus.ToPatchDownloadStatus(patchRest.DownloadStatus)
	approvalStatus = approvalStatus.ToPatchApprovedStatus(patchRest.PatchApprovalStatus)
	testStatus = testStatus.ToPatchTestStatus(patchRest.PatchTestStatus)
	patchSeverity = patchSeverity.ToPatchSeverity(strings.ToLower(patchRest.PatchSeverity))
	patchUpdateCategory = patchUpdateCategory.ToPatchCategory(strings.ToLower(patchRest.PatchUpdateCategory))
	rebootBehaviour = rebootBehaviour.ToRebootBehaviour(strings.ToLower(patchRest.RebootBehaviour))
	source = source.ToPatchSource(patchRest.Source)

	return &model.Patch{
		BaseEntityModel:            ConvertToBaseEntityModel(patchRest.BaseEntityRest),
		Title:                      patchRest.Title,
		OsPlatform:                 osType,
		OsArch:                     osArch,
		OsApplicationId:            patchRest.OsApplicationId,
		VendorId:                   patchRest.VendorId,
		DownloadStatus:             downloadStatus,
		DownloadError:              patchRest.DownloadError,
		DownloadSize:               patchRest.DownloadSize,
		PatchApprovalStatus:        approvalStatus,
		PatchTestStatus:            testStatus,
		ApprovedOn:                 patchRest.ApprovedOn,
		ApprovedBy:                 patchRest.ApprovedBy,
		Description:                patchRest.Description,
		AffectedProducts:           patchRest.AffectedProducts,
		Tags:                       patchRest.Tags,
		DownloadOn:                 patchRest.DownloadOn,
		BulletinId:                 patchRest.BulletinId,
		CVENumber:                  patchRest.CVENumber,
		KbId:                       patchRest.KbId,
		PatchSeverity:              patchSeverity,
		PatchUpdateCategory:        patchUpdateCategory,
		SupportUrl:                 patchRest.SupportUrl,
		LanguageSupported:          patchRest.LanguageSupported,
		RebootBehaviour:            rebootBehaviour,
		IsUninstallable:            patchRest.IsUninstallable,
		HasSupersededUpdates:       patchRest.HasSupersededUpdates,
		IsSuperseded:               patchRest.IsSuperseded,
		DownloadFileDetails:        patchRest.DownloadFileDetails,
		UUID:                       patchRest.UUID,
		Status:                     status.ToPatchStatus(patchRest.Status),
		Source:                     source,
		ReleaseDate:                patchRest.ReleaseDate,
		ProductType:                patchRest.ProductType,
		InstallCommand:             patchRest.InstallCommand,
		UninstallCommand:           patchRest.UninstallCommand,
		UpgradeCommand:             patchRest.UpgradeCommand,
		AtLeastOneFileInstallation: patchRest.AtLeastOneFileInstallation,
		PackageNames:               patchRest.PackageNames,
	}, nil
}

func (service PatchService) convertToRest(patchEndpointCounts map[int64]map[int64]int64, patchInfo model.Patch) rest.PatchRest {
	downloadSize := int64(0)
	missingEndPoints := int64(0)
	installedEndPoints := int64(0)
	ignoredEndPoints := int64(0)
	rollback := "Not Supported"
	for _, detail := range patchInfo.DownloadFileDetails {
		downloadSize += detail.Size
	}

	if patchEndpointCounts != nil && len(patchEndpointCounts) > 0 {
		if countMap, ok := patchEndpointCounts[patchInfo.Id]; ok {
			if count, ok := countMap[int64(model.Missing)]; ok {
				missingEndPoints = count
			}
			if count, ok := countMap[int64(model.Installed)]; ok {
				installedEndPoints = count
			}
			if count, ok := countMap[int64(model.Ignored)]; ok {
				ignoredEndPoints = count
			}
		}
	}
	isThirdParty := false
	if len(patchInfo.Tags) > 0 {
		for _, tag := range patchInfo.Tags {
			if strings.Contains(tag, "Third Party") {
				isThirdParty = true
				break
			}
		}
	}
	if patchInfo.PackageNames != nil && len(patchInfo.PackageNames) > 0 {
		rollback = "Supported"
	}
	return rest.PatchRest{
		BaseEntityRest:             ConvertToBaseEntityRest(patchInfo.BaseEntityModel),
		Title:                      patchInfo.Title,
		OsPlatform:                 patchInfo.OsPlatform.String(),
		OsArch:                     patchInfo.OsArch.String(),
		OsApplicationId:            patchInfo.OsApplicationId,
		VendorId:                   patchInfo.VendorId,
		DownloadStatus:             patchInfo.DownloadStatus.String(),
		DownloadError:              patchInfo.DownloadError,
		DownloadSize:               downloadSize,
		PatchApprovalStatus:        patchInfo.PatchApprovalStatus.String(),
		PatchTestStatus:            patchInfo.PatchTestStatus.String(),
		ApprovedOn:                 patchInfo.ApprovedOn,
		ApprovedBy:                 patchInfo.ApprovedBy,
		Description:                patchInfo.Description,
		AffectedProducts:           patchInfo.AffectedProducts,
		Tags:                       patchInfo.Tags,
		DownloadOn:                 patchInfo.DownloadOn,
		BulletinId:                 patchInfo.BulletinId,
		CVENumber:                  patchInfo.CVENumber,
		KbId:                       patchInfo.KbId,
		PatchSeverity:              patchInfo.PatchSeverity.String(),
		PatchUpdateCategory:        patchInfo.PatchUpdateCategory.String(),
		SupportUrl:                 patchInfo.SupportUrl,
		LanguageSupported:          patchInfo.LanguageSupported,
		RebootBehaviour:            patchInfo.RebootBehaviour.String(),
		IsUninstallable:            patchInfo.IsUninstallable,
		HasSupersededUpdates:       patchInfo.HasSupersededUpdates,
		IsSuperseded:               patchInfo.IsSuperseded,
		DownloadFileDetails:        patchInfo.DownloadFileDetails,
		UUID:                       patchInfo.UUID,
		Status:                     patchInfo.Status.String(),
		Source:                     patchInfo.Source.String(),
		ReleaseDate:                patchInfo.ReleaseDate,
		ProductType:                patchInfo.ProductType,
		MissingEndpoints:           missingEndPoints,
		InstalledEndpoints:         installedEndPoints,
		IgnoredEndpoints:           ignoredEndPoints,
		IsThirdParty:               isThirdParty,
		InstallCommand:             patchInfo.InstallCommand,
		UninstallCommand:           patchInfo.UninstallCommand,
		UpgradeCommand:             patchInfo.UpgradeCommand,
		AtLeastOneFileInstallation: patchInfo.AtLeastOneFileInstallation,
		PackageNames:               patchInfo.PackageNames,
		Rollback:                   rollback,
	}
}

func (service PatchService) convertToPatchRest(patchInfo model.Patch) rest.PatchRest {
	downloadSize := int64(0)
	rollback := "Not Supported"
	for _, detail := range patchInfo.DownloadFileDetails {
		downloadSize += detail.Size
	}
	isThirdParty := false
	if len(patchInfo.Tags) > 0 {
		for _, tag := range patchInfo.Tags {
			if strings.Contains(tag, "Third Party") {
				isThirdParty = true
				break
			}
		}
	}
	if patchInfo.PackageNames != nil && len(patchInfo.PackageNames) > 0 {
		rollback = "Supported"
	}
	return rest.PatchRest{
		BaseEntityRest:             ConvertToBaseEntityRest(patchInfo.BaseEntityModel),
		Title:                      patchInfo.Title,
		OsPlatform:                 patchInfo.OsPlatform.String(),
		OsArch:                     patchInfo.OsArch.String(),
		OsApplicationId:            patchInfo.OsApplicationId,
		VendorId:                   patchInfo.VendorId,
		DownloadStatus:             patchInfo.DownloadStatus.String(),
		DownloadError:              patchInfo.DownloadError,
		DownloadSize:               downloadSize,
		PatchApprovalStatus:        patchInfo.PatchApprovalStatus.String(),
		PatchTestStatus:            patchInfo.PatchTestStatus.String(),
		ApprovedOn:                 patchInfo.ApprovedOn,
		ApprovedBy:                 patchInfo.ApprovedBy,
		Description:                patchInfo.Description,
		AffectedProducts:           patchInfo.AffectedProducts,
		Tags:                       patchInfo.Tags,
		DownloadOn:                 patchInfo.DownloadOn,
		BulletinId:                 patchInfo.BulletinId,
		CVENumber:                  patchInfo.CVENumber,
		KbId:                       patchInfo.KbId,
		PatchSeverity:              patchInfo.PatchSeverity.String(),
		PatchUpdateCategory:        patchInfo.PatchUpdateCategory.String(),
		SupportUrl:                 patchInfo.SupportUrl,
		LanguageSupported:          patchInfo.LanguageSupported,
		RebootBehaviour:            patchInfo.RebootBehaviour.String(),
		IsUninstallable:            patchInfo.IsUninstallable,
		HasSupersededUpdates:       patchInfo.HasSupersededUpdates,
		IsSuperseded:               patchInfo.IsSuperseded,
		DownloadFileDetails:        patchInfo.DownloadFileDetails,
		UUID:                       patchInfo.UUID,
		Status:                     patchInfo.Status.String(),
		Source:                     patchInfo.Source.String(),
		ReleaseDate:                patchInfo.ReleaseDate,
		ProductType:                patchInfo.ProductType,
		IsThirdParty:               isThirdParty,
		InstallCommand:             patchInfo.InstallCommand,
		UninstallCommand:           patchInfo.UninstallCommand,
		UpgradeCommand:             patchInfo.UpgradeCommand,
		AtLeastOneFileInstallation: patchInfo.AtLeastOneFileInstallation,
		PackageNames:               patchInfo.PackageNames,
		Rollback:                   rollback,
	}
}

func (service PatchService) Create(patchRest rest.PatchRest, state model.PatchState) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Process started to create patch")
	patchRest.CreatedTime = common.CurrentMillisecond()
	patchRest.CreatedById = common.GetUserFromCallContext()

	patch, err := service.convertToModel(patchRest)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating patch ,Error : %s ", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	id, err := service.Repository.Create(patch)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating patch ,Error : %s ", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	go service.AfterCreate(patch, state)

	logger.ServiceLogger.Info("Create patch process completed successfully")
	return id, common.CustomError{}
}

func (service PatchService) Update(id int64, restModel rest.PatchRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update patch with id - %v", id))
	patch, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating patch for id - %v ,Error : %s ", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	if restModel.PatchMap["downloadFileDetails"] != nil && patch.PatchUpdateCategory == model.UPGRADES {
		fileDetailsMap := restModel.PatchMap["downloadFileDetails"]
		if fileDetailsMap != nil {
			var isValid bool
			switch values := fileDetailsMap.(type) {
			case []interface{}:
				for _, item := range values {
					if fileDetail, ok := item.(map[string]interface{}); ok {
						if strings.HasSuffix(fileDetail["fileName"].(string), ".iso") {
							pchReference, _ := NewPatchPreferenceService().Get()
							if model.PRE_APPROVED.String() == pchReference.PatchApprovalPolicy {
								restModel.PatchApprovalStatus = model.APPROVED.String()
							} else if model.MANUAL_APPROVED.String() == pchReference.PatchApprovalPolicy {
								restModel.PatchApprovalStatus = model.NOT_APPROVED.String()
							} else if model.TEST_AND_APPROVED.String() == pchReference.PatchApprovalPolicy {
								restModel.PatchApprovalStatus = model.NOT_APPROVED.String()
								restModel.PatchTestStatus = model.NOT_TESTED.String()
							}
							restModel.PatchMap["patchApprovalStatus"] = restModel.PatchApprovalStatus
							restModel.PatchMap["patchTestStatus"] = restModel.PatchTestStatus
							isValid = true
							break
						}
					}
				}
			case []model.PatchFileData:
				for _, fileDetail := range values {
					if strings.HasSuffix(fileDetail.FileName, ".iso") {
						pchReference, _ := NewPatchPreferenceService().Get()
						if model.PRE_APPROVED.String() == pchReference.PatchApprovalPolicy {
							restModel.PatchApprovalStatus = model.APPROVED.String()
						} else if model.MANUAL_APPROVED.String() == pchReference.PatchApprovalPolicy {
							restModel.PatchApprovalStatus = model.NOT_APPROVED.String()
						} else if model.TEST_AND_APPROVED.String() == pchReference.PatchApprovalPolicy {
							restModel.PatchApprovalStatus = model.NOT_APPROVED.String()
							restModel.PatchTestStatus = model.NOT_TESTED.String()
						}
						restModel.PatchMap["patchApprovalStatus"] = restModel.PatchApprovalStatus
						restModel.PatchMap["patchTestStatus"] = restModel.PatchTestStatus
						isValid = true
						break
					}
				}
			}
			if !isValid {
				return false, common.CustomError{Message: "Invalid file type", Code: http.StatusBadRequest}
			}
		}
	}

	if restModel.PatchMap["patchApprovalStatus"] != nil {
		var approvalStatus model.PatchApprovalStatus
		approvalStatus = approvalStatus.ToPatchApprovedStatus(restModel.PatchApprovalStatus)
		if patch.PatchApprovalStatus != approvalStatus {
			if restModel.PatchApprovalStatus == model.APPROVED.String() {
				restModel.ApprovedOn = time.Now().UnixMilli()
				restModel.ApprovedBy = common.GetUserFromCallContext()
			} else {
				restModel.ApprovedOn = 0
				restModel.ApprovedBy = 0
			}
			restModel.PatchMap["approvedOn"] = restModel.ApprovedOn
			restModel.PatchMap["approvedBy"] = restModel.ApprovedBy
		}
	}

	patch.UpdatedTime = common.CurrentMillisecond()
	patch.UpdatedById = common.GetUserFromCallContext()
	_, isUpdatable := service.performPartialUpdate(&patch, restModel)
	if isUpdatable {

		_, err := service.Repository.Update(&patch)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error while updating patch for id - %v ,Error : %s ", id, err.Error()))
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}

		logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to update patch with id - %v", id))
		return true, common.CustomError{}
	} else {
		logger.ServiceLogger.Info(fmt.Sprintf("No fields need to updated"))
		logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to update patch with id - %v", id))
		return isUpdatable, common.CustomError{}
	}
}

func (service PatchService) GetPatch(id int64, includeArchive bool) (rest.PatchRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get Patch for id %v", id))
	var patchRest rest.PatchRest
	patch, err := service.Repository.GetById(id, includeArchive)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while get patch for id - %v, Error : %s ", id, err.Error()))
		return patchRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to get Patch for id %v", id))
	return service.convertToPatchRest(patch), nil
}

func (service PatchService) GetPatchByUuid(uuid string) (rest.PatchRest, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to get Patch for uuid : %v", uuid))
	var patchRest rest.PatchRest
	patch, err := service.Repository.GetByUuid(uuid)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while get patch for uuid - %v, Error : %s ", uuid, err.Error()))
		return patchRest, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to get Patch for uuid %v", uuid))
	return service.convertToPatchRest(patch), nil
}

func isValidPatch(fileDetails []model.PatchFileData) bool {
	if len(fileDetails) == 0 {
		return false
	}

	isAnyMSUFile := false
	isAnyUrlEmpty := false
	isWimFilePresent := false

	for _, detail := range fileDetails {
		if detail.FetchFromMSU {
			isAnyMSUFile = true
			break
		}

		if detail.DownloadUrl == "" {
			isAnyUrlEmpty = true
		}

		if detail.DownloadUrl != "" && strings.HasSuffix(detail.DownloadUrl, ".wim") {
			isWimFilePresent = true
		}
	}

	return isAnyMSUFile || (!isAnyUrlEmpty && !isWimFilePresent)
}

func (service PatchService) CreatePatchFromWindowsPatch(wPatch rest.WindowsPatchRest, state model.PatchState) (rest.PatchRest, error) {
	pch, err := service.Repository.GetByUuid(wPatch.UUID)
	if err == nil || pch.Id > 0 {
		if wPatch.CveNumber != "" {
			pch.CVENumber = wPatch.CveNumber
			_, err = service.Repository.Update(&pch)
			if err != nil {
				logger.ServiceLogger.Error("[CreatePatchFromWindowsPatch]", err)
			}
		}
		return service.convertToPatchRest(pch), err
	}

	// Validate if the patch is valid
	if !isValidPatch(wPatch.FileDetails) && !strings.EqualFold(wPatch.Classification, model.UPGRADES.String()) {
		return rest.PatchRest{}, errors.New("invalid patch: file details validation failed")
	}

	patchRest := rest.PatchRest{}
	patchRest.UUID = wPatch.UUID
	patchRest.KbId = wPatch.KbId
	patchRest.Title = wPatch.Title
	patchRest.Description = wPatch.Description
	patchRest.OsPlatform = common.Windows.String()
	patchRest.OsArch = wPatch.Arch
	patchRest.ReleaseDate = wPatch.ReleaseDate

	patchRest.BulletinId = wPatch.BulletinId
	patchRest.IsUninstallable = wPatch.CanUninstall
	patchRest.PatchSeverity = wPatch.Severity
	patchRest.AtLeastOneFileInstallation = wPatch.AtLeastOneFileInstallation
	patchRest.PackageNames = wPatch.PackageNames
	patchRest.Source = model.Scanning.String()

	existingFileMap := map[string]interface{}{}
	var newFileDetails []model.PatchFileData
	if strings.EqualFold(wPatch.Classification, model.UPGRADES.String()) {
		patchRest.PatchApprovalStatus = model.PENDING_UPLOAD.String()
	} else {
		patchRest.PatchApprovalStatus = model.NOT_APPROVED.String()
		fileDetails := wPatch.FileDetails
		if len(fileDetails) > 0 {
			/*for _, fileDetail := range fileDetails {
				if _, ok := existingFileMap[fileDetail.FileName]; !ok && !strings.Contains(strings.ToLower(fileDetail.FileName), ".psf") && !strings.Contains(strings.ToLower(fileDetail.FileName), ".wim") && (strings.Contains(strings.ToLower(fileDetail.FileName), ".exe") || strings.Contains(strings.ToLower(fileDetail.FileName), ".msi") || strings.Contains(strings.ToLower(fileDetail.FileName), ".msu") || strings.Contains(strings.ToLower(fileDetail.FileName), ".cab")) {
					existingFileMap[fileDetail.FileName] = true
					newFileDetails = append(newFileDetails, fileDetail)
				}
			}*/
			// Check if any file has FetchFromMSU set to true
			hasMSUFiles := false
			var msuFiles []model.PatchFileData

			for _, fileDetail := range fileDetails {
				if fileDetail.FetchFromMSU {
					hasMSUFiles = true
					if _, ok := existingFileMap[fileDetail.FileName]; !ok {
						existingFileMap[fileDetail.FileName] = true
						msuFiles = append(msuFiles, fileDetail)
					}
				}
			}

			// If MSU files exist, only use those
			if hasMSUFiles {
				newFileDetails = msuFiles
			} else {
				// Otherwise use all files except .psf files
				for _, fileDetail := range fileDetails {
					if _, ok := existingFileMap[fileDetail.FileName]; !ok && !strings.Contains(strings.ToLower(fileDetail.FileName), ".psf") {
						existingFileMap[fileDetail.FileName] = true
						newFileDetails = append(newFileDetails, fileDetail)
					}
				}
			}
		}
	}

	patchRest.DownloadFileDetails = newFileDetails
	patchRest.RebootBehaviour = wPatch.RestartBehaviour
	patchRest.SupportUrl = wPatch.MoreInfoUrl
	patchRest.PatchUpdateCategory = wPatch.Classification
	patchRest.CVENumber = wPatch.CveNumber
	patchRest.IsSuperseded = wPatch.ReplaceById != 0
	patchRest.HasSupersededUpdates = wPatch.SupersedesString != ""
	if wPatch.AffectedProduct != nil && len(wPatch.AffectedProduct) > 0 {
		var affectedProducts []int64
		for _, uuid := range wPatch.AffectedProduct {
			app, err := NewPatchOsApplicationService().Repository.GetByUUID(strings.TrimSpace(uuid))
			if err == nil {
				affectedProducts = append(affectedProducts, app.Id)
			}
		}

		if len(affectedProducts) > 0 {
			patchRest.AffectedProducts = affectedProducts
		}
	}
	//patchRest.LanguageSupported = wPatch.SupportedLanguage
	// TODO handle pending fields

	pchId, createErr := service.Create(patchRest, state)
	if createErr.Message == "" {
		return service.GetPatch(pchId, false)
	}

	return patchRest, errors.New(createErr.Message)
}

func (service PatchService) CreatePatchFromThirdPartyPatch(thirdPartyPatch rest.ThirdPartyPackageRest, state model.PatchState, osPlatform string) (rest.PatchRest, error) {
	pch, err := service.Repository.GetByUuid(thirdPartyPatch.Uuid)
	if err == nil || pch.Id > 0 {
		if thirdPartyPatch.CveDetails != nil {
			cveId := ""
			for i, cveDetail := range thirdPartyPatch.CveDetails {
				if i != 0 {
					cveId += ", "
				}
				cveId += cveDetail.CveId
			}
			pch.CVENumber = cveId
			_, err = service.Repository.Update(&pch)
			if err != nil {
				logger.ServiceLogger.Error("[CreatePatchFromThirdPartyPatch]", err)
			}
		}
		return rest.PatchRest{BaseEntityRest: rest.BaseEntityRest{Id: pch.Id}}, err
	}

	patchRest := rest.PatchRest{}
	patchRest.UUID = thirdPartyPatch.Uuid
	patchRest.Title = thirdPartyPatch.Name + "(" + thirdPartyPatch.Version + ")"
	patchRest.Description = thirdPartyPatch.Description
	patchRest.OsPlatform = osPlatform
	patchRest.OsArch = thirdPartyPatch.Arch
	patchRest.ReleaseDate = thirdPartyPatch.ReleaseDate
	patchRest.PatchApprovalStatus = model.NOT_APPROVED.String()
	patchRest.Source = model.Scanning.String()
	patchRest.InstallCommand = thirdPartyPatch.InstallCommand
	patchRest.UninstallCommand = thirdPartyPatch.UnInstallCommand

	existingFileMap := map[string]interface{}{}
	var newFileDetails []model.PatchFileData
	fileDetails := thirdPartyPatch.PkgFileData
	if len(fileDetails) > 0 {
		for _, fileDetail := range fileDetails {
			if _, ok := existingFileMap[fileDetail.FileName]; !ok {
				existingFileMap[fileDetail.FileName] = true
				newFileDetails = append(newFileDetails, fileDetail)
			}
		}
	}
	patchRest.DownloadFileDetails = newFileDetails
	patchRest.SupportUrl = thirdPartyPatch.SupportUrl
	patchRest.Tags = []string{"Third Party"}

	pchId, createErr := service.Create(patchRest, state)
	if createErr.Message == "" {
		return service.GetPatch(pchId, false)
	}
	return patchRest, errors.New(createErr.Message)
}

func (service PatchService) CreatePatchFromMacPatch(mPatch rest.MacPatchRest, osVersion string, patchState model.PatchState, checkVersionCompatibility bool) (rest.PatchRest, error) {
	if mPatch.ProductType == "macOsVersionUpdate" && patchState == model.Missing && checkVersionCompatibility {
		oVersion := "v" + osVersion
		patchVersion := "v" + mPatch.Version
		if semver.IsValid(oVersion) && semver.IsValid(patchVersion) {
			if semver.Compare(oVersion, patchVersion) >= 0 {
				var pkg rest.PatchRest
				logger.PatchPoolingLogger.Debug("Not creating patch as patch version is %s, and current version is %s",
					mPatch.Version, osVersion)
				return pkg, nil
			}
		}

	}
	patch, err := service.GetPatchByUuid(mPatch.ProductKey)
	if err == nil || patch.Id > 0 {
		return patch, err
	}
	var rebootBehaviour model.RebootBehaviour
	var osArch common.OsArchitecture
	patchRest := rest.PatchRest{}
	patchRest.UUID = mPatch.ProductKey
	patchRest.ProductType = mPatch.ProductType
	patchRest.BulletinId = mPatch.ProductKey
	patchRest.Title = mPatch.Name
	patchRest.Description = mPatch.Description
	patchRest.OsPlatform = common.MacOS.String()
	patchRest.OsArch = osArch.ToOsArch("all").String()
	patchRest.ReleaseDate = mPatch.ReleaseDate
	patchRest.PatchApprovalStatus = model.NOT_APPROVED.String()
	patchRest.PatchSeverity = model.PS_UNSPECIFIED.String()
	if mPatch.ProductType == "macOsVersionUpdate" {
		patchRest.RebootBehaviour = rebootBehaviour.ToRebootBehaviour("yes").String()
	} else {
		patchRest.RebootBehaviour = rebootBehaviour.ToRebootBehaviour("may_be").String()
	}
	patchRest.Source = model.Scanning.String()
	var affectedProducts []int64
	patchRest.DownloadFileDetails = mPatch.Packages
	if mPatch.ProductType != "macOsVersionUpdate" {
		application, _ := NewPatchOsApplicationService().Repository.GetByUUID(strings.TrimSpace(mPatch.ProductKey))
		if application.Id == 0 {
			application.ProductType = model.APPLICATION
			application.Platform = common.MacOS
			application.Hidden = true
			application.Name = mPatch.Name
			application.UUID = mPatch.ProductKey
			id, err := NewPatchOsApplicationService().Repository.Create(&application)
			if err == nil {
				affectedProducts = append(affectedProducts, id)
			}
		} else {
			affectedProducts = append(affectedProducts, application.Id)
		}
	}

	osVersions := strings.Split(mPatch.OsVersion, ",")
	for _, macOsVersion := range osVersions {
		name := "macOS " + macOsVersion
		application, _ := NewPatchOsApplicationService().Repository.GetByUUID(name)
		if application.Id == 0 {
			application.ProductType = model.APPLICATION
			application.Platform = common.MacOS
			application.Hidden = true
			application.Name = name
			application.UUID = name
			id, err := NewPatchOsApplicationService().Repository.Create(&application)
			if err == nil {
				affectedProducts = append(affectedProducts, id)
			}
		} else {
			affectedProducts = append(affectedProducts, application.Id)
		}
	}

	if len(affectedProducts) > 0 {
		patchRest.AffectedProducts = affectedProducts
	}

	pchId, createErr := service.Create(patchRest, patchState)
	if createErr.Message == "" {
		return service.GetPatch(pchId, false)
	}

	return patchRest, errors.New(createErr.Message)
}

func (service PatchService) DeletePatch(id int64) (bool, error) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to delete patch for id %v", id))
	patch, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while delete patch for id - %v, Error : %s ", id, err.Error()))
		return false, err
	}

	_, err = service.Repository.DeleteById(patch.Id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while delete patch for id - %v ,Error : %s ", id, err.Error()))
		return false, err
	}
	logger.ServiceLogger.Info(fmt.Sprintf("Process Completed to delete patch for id %v", id))
	return true, nil
}

func (service PatchService) performPartialUpdate(domainModel *model.Patch, restModel rest.PatchRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBase(&domainModel.BaseEntityModel, restModel.BaseEntityRest)

	restModel.InstallCommand = domainModel.InstallCommand
	common.PrepareInDiffMap("installcommand", domainModel.InstallCommand, restModel.InstallCommand, &diffMap)
	restModel.UninstallCommand = domainModel.UninstallCommand
	common.PrepareInDiffMap("uninstallcommand", domainModel.UninstallCommand, restModel.UninstallCommand, &diffMap)
	restModel.UpgradeCommand = domainModel.UpgradeCommand
	common.PrepareInDiffMap("upgradecommand", domainModel.UpgradeCommand, restModel.UpgradeCommand, &diffMap)

	if restModel.PatchMap["title"] != nil && domainModel.Title != restModel.Title {
		common.PrepareInDiffMap("title", domainModel.Title, restModel.Title, &diffMap)
		domainModel.Title = restModel.Title
	}

	if restModel.PatchMap["downloadError"] != nil && domainModel.DownloadError != restModel.DownloadError {
		common.PrepareInDiffMap("downloadError", domainModel.DownloadError, restModel.DownloadError, &diffMap)
		domainModel.DownloadError = restModel.DownloadError
	}

	if restModel.PatchMap["description"] != nil && domainModel.Description != restModel.Description {
		common.PrepareInDiffMap("description", domainModel.Description, restModel.Description, &diffMap)
		domainModel.Description = restModel.Description
	}

	if restModel.PatchMap["affectedProducts"] != nil && !reflect.DeepEqual(domainModel.AffectedProducts, restModel.AffectedProducts) {
		common.PrepareInDiffMap("affected_products", domainModel.AffectedProducts, restModel.AffectedProducts, &diffMap)
		domainModel.AffectedProducts = restModel.AffectedProducts
	}

	if restModel.PatchMap["tags"] != nil && !reflect.DeepEqual(domainModel.Tags, restModel.Tags) {
		common.PrepareInDiffMap("tags", domainModel.Tags, restModel.Tags, &diffMap)
		domainModel.Tags = restModel.Tags
	}

	if restModel.PatchMap["downloadStatus"] != nil {
		var downloadStatus model.PatchDownloadStatus
		downloadStatus = downloadStatus.ToPatchDownloadStatus(restModel.DownloadStatus)
		if domainModel.DownloadStatus != downloadStatus {
			common.PrepareInDiffMap("download_status", domainModel.DownloadStatus.String(), restModel.DownloadStatus, &diffMap)
			domainModel.DownloadStatus = downloadStatus
		}
	}

	if restModel.PatchMap["patchApprovalStatus"] != nil {
		var approvalStatus model.PatchApprovalStatus
		approvalStatus = approvalStatus.ToPatchApprovedStatus(restModel.PatchApprovalStatus)
		if domainModel.PatchApprovalStatus != approvalStatus {
			common.PrepareInDiffMap("patch_approval_status", domainModel.PatchApprovalStatus.String(), restModel.PatchApprovalStatus, &diffMap)
			domainModel.PatchApprovalStatus = approvalStatus
		}
	}

	if restModel.PatchMap["status"] != nil {
		var status model.PatchStatus
		status = status.ToPatchStatus(restModel.Status)
		if domainModel.Status != status {
			common.PrepareInDiffMap("status", domainModel.Status.String(), restModel.Status, &diffMap)
			domainModel.Status = status
		}
	}

	if restModel.PatchMap["patchTestStatus"] != nil {
		var testStatus model.PatchTestStatus
		testStatus = testStatus.ToPatchTestStatus(restModel.PatchTestStatus)
		if domainModel.PatchTestStatus != testStatus {
			common.PrepareInDiffMap("patch_test_status", domainModel.PatchTestStatus.String(), restModel.PatchTestStatus, &diffMap)
			domainModel.PatchTestStatus = testStatus
		}
	}

	if restModel.PatchMap["approvedOn"] != nil && domainModel.ApprovedOn != restModel.ApprovedOn {
		common.PrepareInDiffMap("approved_on", domainModel.ApprovedOn, restModel.ApprovedOn, &diffMap)
		domainModel.ApprovedOn = restModel.ApprovedOn
	}

	if restModel.PatchMap["approvedBy"] != nil && domainModel.ApprovedBy != restModel.ApprovedBy {
		common.PrepareInDiffMap("approved_by", domainModel.ApprovedBy, restModel.ApprovedBy, &diffMap)
		domainModel.ApprovedBy = restModel.ApprovedBy
	}

	if restModel.PatchMap["downloadOn"] != nil && domainModel.DownloadOn != restModel.DownloadOn {
		common.PrepareInDiffMap("download_on", domainModel.DownloadOn, restModel.DownloadOn, &diffMap)
		domainModel.DownloadOn = restModel.DownloadOn
	}

	if restModel.PatchMap["patchSeverity"] != nil {
		var patchSeverity model.PatchSeverity
		patchSeverity = patchSeverity.ToPatchSeverity(restModel.PatchSeverity)
		if domainModel.PatchSeverity != patchSeverity {
			common.PrepareInDiffMap("patch_severity", domainModel.PatchSeverity.String(), restModel.PatchSeverity, &diffMap)
			domainModel.PatchSeverity = patchSeverity
		}
	}

	if restModel.PatchMap["patchUpdateCategory"] != nil {
		var patchUpdateCategory model.PatchUpdateCategory
		patchUpdateCategory = patchUpdateCategory.ToPatchCategory(restModel.PatchUpdateCategory)
		if domainModel.PatchUpdateCategory != patchUpdateCategory {
			common.PrepareInDiffMap("patch_update_category", domainModel.PatchUpdateCategory.String(), restModel.PatchUpdateCategory, &diffMap)
			domainModel.PatchUpdateCategory = patchUpdateCategory
		}
	}

	if restModel.PatchMap["rebootBehaviour"] != nil {
		var rebootBehaviour model.RebootBehaviour
		rebootBehaviour = rebootBehaviour.ToRebootBehaviour(restModel.RebootBehaviour)
		if domainModel.RebootBehaviour != rebootBehaviour {
			common.PrepareInDiffMap("reboot_behaviour", domainModel.RebootBehaviour.String(), restModel.RebootBehaviour, &diffMap)
			domainModel.RebootBehaviour = rebootBehaviour
		}
	}

	if restModel.PatchMap["osArch"] != nil {
		var arch common.OsArchitecture
		arch = arch.ToOsArch(restModel.OsArch)
		if domainModel.OsArch != arch {
			common.PrepareInDiffMap("os_arch", domainModel.OsArch.String(), restModel.OsArch, &diffMap)
			domainModel.OsArch = arch
		}
	}

	if restModel.PatchMap["isUninstallable"] != nil && domainModel.IsUninstallable != restModel.IsUninstallable {
		common.PrepareInDiffMap("is_uninstallable", strconv.FormatBool(domainModel.IsUninstallable), strconv.FormatBool(restModel.IsUninstallable), &diffMap)
		domainModel.IsUninstallable = restModel.IsUninstallable
	}

	if restModel.PatchMap["hasSupersededUpdates"] != nil && domainModel.HasSupersededUpdates != restModel.HasSupersededUpdates {
		common.PrepareInDiffMap("has_superseded_updates", strconv.FormatBool(domainModel.HasSupersededUpdates), strconv.FormatBool(restModel.HasSupersededUpdates), &diffMap)
		domainModel.HasSupersededUpdates = restModel.HasSupersededUpdates
	}

	if restModel.PatchMap["isSuperseded"] != nil && domainModel.IsSuperseded != restModel.IsSuperseded {
		common.PrepareInDiffMap("is_superseded", strconv.FormatBool(domainModel.IsSuperseded), strconv.FormatBool(restModel.IsSuperseded), &diffMap)
		domainModel.IsSuperseded = restModel.IsSuperseded
	}

	if restModel.PatchMap["downloadFileDetails"] != nil && !reflect.DeepEqual(domainModel.DownloadFileDetails, restModel.DownloadFileDetails) {
		common.PrepareInDiffMap("download_file_details", domainModel.DownloadFileDetails, restModel.DownloadFileDetails, &diffMap)
		domainModel.DownloadFileDetails = restModel.DownloadFileDetails
	}

	if restModel.PatchMap["releaseDate"] != nil && domainModel.ReleaseDate != restModel.ReleaseDate {
		common.PrepareInDiffMap("release_date", domainModel.ReleaseDate, restModel.ReleaseDate, &diffMap)
		domainModel.ReleaseDate = restModel.ReleaseDate
	}

	return diffMap, len(diffMap) != 0
}

// GetAllPatchOptimized uses JOINs instead of multiple queries for better performance
func (service PatchService) GetAllPatchOptimized(filter rest.SearchFilter, addIsOldCondition bool, countRequired bool) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest

	// Build optimized filter with JOINs
	optimizedFilter := service.buildOptimizedPatchFilter(filter, addIsOldCondition)

	// Use secure parameterized queries with JOIN support
	countQueryResult := rest.PrepareParameterizedQueryFromSearchFilter(optimizedFilter, common.PATCH.String(), true, "")
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)

	if count > 0 {
		searchQueryResult := rest.PrepareParameterizedQueryFromSearchFilter(optimizedFilter, common.PATCH.String(), false, "")
		patchPageList, err := service.Repository.GetAllPatch(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(countRequired, patchPageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}

	responsePage.TotalCount = count
	return responsePage, nil
}

func (service PatchService) GetAllPatch(filter rest.SearchFilter, addIsOldCondition bool) (rest.ListResponseRest, error) {
	qualifications := filter.Qualification
	var responsePage rest.ListResponseRest
	var searchQualifications []rest.Qualification
	if len(qualifications) > 0 {
		for _, qualification := range qualifications {
			if qualification.Column == "scope" {
				var assets []interface{}
				if arr, ok := qualification.Value.([]interface{}); ok && arr != nil {
					assets = qualification.Value.([]interface{})
				}
				platforms := NewAgentService().GetPatchPlatformByScope(qualification.Operator, assets)
				if platforms != nil {
					searchQualifications = append(searchQualifications, rest.BuildQualification("os_platform", "in", platforms, constant.AND.String()))
				}

			} else if qualification.Column == "patch_state" {
				var patchStateQualifications []rest.Qualification
				patchStateQualifications = append(patchStateQualifications, rest.Qualification{
					Column:    "patch_state",
					Operator:  "equals",
					Value:     qualification.Value,
					Condition: "and",
					Type:      "enum",
					Reference: "patchState",
				}, rest.Qualification{
					Column:    "is_old",
					Operator:  "equals",
					Value:     false,
					Condition: "and",
				})
				relationList, err := NewAssetPatchRelationService().GetAllAgentPatchRelation(rest.SearchFilter{Qualification: patchStateQualifications})
				if err == nil && len(relationList) > 0 {
					addIsOldCondition = false
					var pchIds []int64
					seen := make(map[int64]bool)
					for _, relation := range relationList {
						if !seen[relation.PatchId] {
							pchIds = append(pchIds, relation.PatchId)
							seen[relation.PatchId] = true
						}
					}
					searchQualifications = append(searchQualifications, rest.Qualification{
						Column:    "id",
						Operator:  "in",
						Value:     pchIds,
						Condition: "and",
					})
				} else {
					return responsePage, nil
				}
			} else {
				searchQualifications = append(searchQualifications, qualification)
			}
		}
	}

	if addIsOldCondition {
		var qualifications1 []rest.Qualification
		qualifications1 = append(qualifications1, rest.Qualification{
			Column:    "is_old",
			Operator:  "equals",
			Value:     false,
			Condition: "and",
		})
		relationList, err := NewAssetPatchRelationService().GetAllAgentPatchRelation(rest.SearchFilter{Qualification: qualifications1})
		if err == nil && len(relationList) > 0 {
			var pchIds []int64
			for _, relation := range relationList {
				pchIds = append(pchIds, relation.PatchId)
			}
			searchQualifications = append(searchQualifications, rest.Qualification{
				Column:    "id",
				Operator:  "in",
				Value:     pchIds,
				Condition: "and",
			})
		} else {
			return responsePage, nil
		}
	}

	filter.Qualification = searchQualifications
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH.String(), true, "")
	var patchPageList []model.Patch
	var err error
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH.String(), false, "")
		patchPageList, err = service.Repository.GetAllPatch(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
		responsePage.ObjectList = service.convertListToRest(true, patchPageList)
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

// buildOptimizedPatchFilter converts multiple queries into JOIN-based single query
func (service PatchService) buildOptimizedPatchFilter(filter rest.SearchFilter, addIsOldCondition bool) rest.SearchFilter {
	optimizedFilter := filter
	var joins []rest.JoinClause
	var qualifications []rest.Qualification

	needsAssetPatchRelationJoin := false

	// Process existing qualifications
	for _, qualification := range filter.Qualification {
		switch qualification.Column {
		case "scope":
			// Handle scope by joining with agents table
			var assets []interface{}
			if arr, ok := qualification.Value.([]interface{}); ok && arr != nil {
				assets = qualification.Value.([]interface{})
			}
			platforms := NewAgentService().GetPatchPlatformByScope(qualification.Operator, assets)
			if platforms != nil {
				qualifications = append(qualifications, rest.BuildQualification("main.os_platform", "in", platforms, constant.AND.String()))
			}

		case "patch_state", "asset_id", "patchState", "assetId":
			// Handle patch_state by joining with asset_patch_relation
			needsAssetPatchRelationJoin = true
			if qualification.Column == "patch_state" || qualification.Column == "patchState" {
				qualifications = append(qualifications, rest.Qualification{Column: "apr.patch_state", Operator: qualification.Operator, Value: qualification.Value, Condition: "and", Type: "enum", Reference: "patchState"})
			} else if qualification.Column == "asset_id" || qualification.Column == "assetId" {
				qualifications = append(qualifications, rest.Qualification{Column: "apr.asset_id", Operator: qualification.Operator, Value: qualification.Value, Condition: "and"})
			}
			qualifications = append(qualifications, rest.Qualification{Column: "apr.is_old", Operator: "equals", Value: false, Condition: "and"})
			qualifications = append(qualifications, rest.Qualification{Column: "apr.removed", Operator: "equals", Value: false, Condition: "and"})

		default:
			// For other qualifications, prefix with main table alias
			newQualification := qualification
			if !strings.Contains(qualification.Column, ".") {
				newQualification.Column = "main." + qualification.Column
			}
			qualifications = append(qualifications, newQualification)
		}
	}

	// Add asset_patch_relation JOIN if needed
	if needsAssetPatchRelationJoin || addIsOldCondition {
		joins = append(joins, rest.JoinClause{Type: "INNER", Table: "asset_patch_relation", Alias: "apr", Condition: "main.id = apr.patch_id"})

		if addIsOldCondition && !needsAssetPatchRelationJoin {
			qualifications = append(qualifications, rest.Qualification{Column: "apr.is_old", Operator: "equals", Value: false, Condition: "and"})
			qualifications = append(qualifications, rest.Qualification{Column: "apr.removed", Operator: "equals", Value: false, Condition: "and"})
		}
	}

	optimizedFilter.Columns = "main.id,main.name,main.created_by_id, main.created_time, main.updated_by_id, main.updated_time, main.oob, main.removed, main.title, main.os_platform, main.os_arch, main.os_application_id, main.vendor_id, main.download_status, main.download_error, main.download_size, main.patch_approval_status, " +
		"main.patch_test_status, main.approved_on, main.approved_by, main.description, main.affected_products, main.tags, main.download_on, main.bulletin_id, main.cve_number, main.kb_id, main.patch_severity, " +
		"main.patch_update_category, main.support_url, main.language_supported, main.reboot_behaviour, main.is_uninstallable, main.has_superseded_updates, main.is_superseded, main.download_file_details, main.uuid, " +
		"main.status, main.source, main.release_date, main.product_type, main.install_command, main.uninstall_command, main.upgrade_command, main.at_least_one_file_installation, main.package_names"
	optimizedFilter.Joins = joins
	optimizedFilter.Qualification = qualifications
	optimizedFilter.SortBy = filter.SortBy
	if optimizedFilter.SortBy == "" {
		optimizedFilter.SortBy = "main.id"
	}
	optimizedFilter.Distinct = true
	return optimizedFilter
}

func (service PatchService) GetAllPatches(filter rest.SearchFilter) ([]rest.PatchRest, error) {
	var patchRests []rest.PatchRest
	var patches []model.Patch
	var err error
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.PATCH.String(), false, "")
	patches, err = service.Repository.GetAllPatch(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return patchRests, err
	}
	patchRests = service.convertListToRest(false, patches)
	return patchRests, nil
}

func (service PatchService) convertListToRest(countRequired bool, patches []model.Patch) []rest.PatchRest {
	var patchRestList []rest.PatchRest
	if len(patches) != 0 {
		if countRequired {
			var patchIds []int64
			for _, patch := range patches {
				patchIds = append(patchIds, patch.Id)
			}
			var patchEndpointCounts map[int64]map[int64]int64
			if len(patchIds) > 0 {
				var patchStates []int64
				patchStates = append(patchStates, int64(model.Missing))
				patchStates = append(patchStates, int64(model.Installed))
				patchStates = append(patchStates, int64(model.Ignored))
				filter := rest.SearchFilter{
					Qualification: []rest.Qualification{
						rest.BuildQualification("patch_id", "in", patchIds, "and"),
						rest.BuildQualification("patch_state", "in", patchStates, "and"),
						rest.BuildQualification("is_old", "equals", false, "and"),
					},
				}
				filter.Columns = "count(asset_id) as total,patch_id,patch_state"
				filter.GroupBy = "patch_id,patch_state"
				filter.SortBy = "total"
				countQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.ASSET_PATCH_RELATION.String(), false, "")
				result, _ := NewAssetPatchRelationService().Repository.GetAllAgentPatchRelations(countQuery.Query, countQuery.Parameters)
				if result != nil {
					patchEndpointCounts = make(map[int64]map[int64]int64)
					for _, row := range result {
						patchID, ok1 := row["patch_id"].(int64)
						patchState, ok2 := row["patch_state"].(int64)
						total, ok3 := row["total"].(int64)

						if !ok1 || !ok2 || !ok3 {
							logger.ServiceLogger.Debug("Invalid type conversion: %+v", row)
							continue
						}
						if _, exists := patchEndpointCounts[patchID]; !exists {
							patchEndpointCounts[patchID] = make(map[int64]int64)
						}
						patchEndpointCounts[patchID][patchState] = total
					}
				}
			}

			for _, patch := range patches {
				patchRest := service.convertToRest(patchEndpointCounts, patch)
				patchRestList = append(patchRestList, patchRest)
			}

		} else {
			for _, patch := range patches {
				patchRest := service.convertToPatchRest(patch)
				patchRestList = append(patchRestList, patchRest)
			}
		}
	}
	return patchRestList
}

func (service PatchService) AfterCreate(pkg *model.Patch, state model.PatchState) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while performing after create process for patch id : ", pkg.Id)
		}
	}()
	patchIdentifier := "W"
	if pkg.OsPlatform == common.Linux || pkg.OsPlatform == common.Ubuntu {
		patchIdentifier = "U"
	} else if pkg.OsPlatform == common.MacOS {
		patchIdentifier = "M"
	}

	if pkg.PatchUpdateCategory != model.UPGRADES {
		pchReference, _ := NewPatchPreferenceService().Get()
		if model.PRE_APPROVED.String() == pchReference.PatchApprovalPolicy {
			pkg.PatchApprovalStatus = model.APPROVED
		} else if model.MANUAL_APPROVED.String() == pchReference.PatchApprovalPolicy {
			pkg.PatchApprovalStatus = model.NOT_APPROVED
		} else if model.TEST_AND_APPROVED.String() == pchReference.PatchApprovalPolicy {
			if state == model.Missing {
				pkg.PatchApprovalStatus = model.NOT_APPROVED
				pkg.PatchTestStatus = model.NOT_TESTED
			} else {
				pkg.PatchApprovalStatus = model.APPROVED
			}
		}
	}
	pkg.Name = fmt.Sprintf("ZPH-%s-%04d", patchIdentifier, pkg.Id)
	_, err := service.Repository.Update(pkg)
	if err != nil {
		logger.ServiceLogger.Error("[AfterCreate]", err)
		return
	}
}

func (service PatchService) GetSupersededByPatchListByPchId(uuid string) []map[string]interface{} {
	var SupersededPchList []map[string]interface{}

	filter := rest.SearchFilter{
		Qualification: []rest.Qualification{
			{
				Column:   "supersedesString",
				Operator: "contains",
				Value:    uuid,
			},
		}}
	wPatchList, err := NewWindowsPatchService().GetAllWindowsPatches(filter)
	if err == nil && len(wPatchList) > 0 {
		for _, pch := range wPatchList {
			SupersededPchList = append(SupersededPchList, map[string]interface{}{"kbid": pch.KbId, "uuid": pch.UUID})
		}
	}

	return SupersededPchList
}

func (service PatchService) GetSupersededPatchListByPchId(uuid string) []map[string]interface{} {
	var SupersededPchList []map[string]interface{}
	wPatch, err := NewWindowsPatchService().GetWindowsPatchByUuid(uuid)
	if err == nil && wPatch.SupersedesString != "" {
		supersededUuidList := strings.Split(wPatch.SupersedesString, ",")
		for _, sUUID := range supersededUuidList {
			sPatch, err := NewWindowsPatchService().GetWindowsPatchByUuid(strings.TrimSpace(sUUID))
			if err == nil {
				SupersededPchList = append(SupersededPchList, map[string]interface{}{"kbid": sPatch.KbId, "uuid": sPatch.UUID})
			}
		}
	}

	return SupersededPchList
}

func (service PatchService) UpdatePatchDownloadStatus(name string, pchFileDetails model.PatchFileDownloadStatus) {
	patch, err := service.Repository.GetByName(name)
	if err == nil {
		patchRest := service.convertToPatchRest(patch)
		if pchFileDetails.IsDownloadFailed {
			patchRest.DownloadStatus = model.FAILED.String()
			patchRest.DownloadError = pchFileDetails.Error
			patchRest.PatchMap = map[string]interface{}{"downloadStatus": model.FAILED.String(), "downloadError": patchRest.DownloadError}
			service.Update(patch.Id, patchRest)
		} else {
			var newPchFileDetails []model.PatchFileData
			for _, detail := range pchFileDetails.PatchFileData {
				detail.DownloadUrl = common.FileServerUrl() + "/download/" + patchRest.Name + "/" + detail.RefName
				detail.RefName = ""
				newPchFileDetails = append(newPchFileDetails, detail)
			}
			patchRest.DownloadFileDetails = newPchFileDetails
			patchRest.DownloadStatus = model.SUCCESS.String()
			patchRest.PatchMap = map[string]interface{}{"downloadFileDetails": newPchFileDetails, "downloadStatus": model.SUCCESS.String()}
			service.Update(patch.Id, patchRest)
		}
	}
}
